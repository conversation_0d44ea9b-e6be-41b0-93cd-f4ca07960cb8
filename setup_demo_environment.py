"""
TicketSage AI Demo Environment Setup Script
Automated setup for demo environment with all required dependencies
"""

import os
import subprocess
import sys
import json
from pathlib import Path

class DemoEnvironmentSetup:
    def __init__(self):
        self.project_root = Path.cwd()
        self.demo_dir = self.project_root / "demo"
        self.docs_dir = self.project_root / "docs"
        self.data_dir = self.project_root / "data"
        
    def setup_directories(self):
        """Create necessary directory structure"""
        directories = [
            self.demo_dir,
            self.demo_dir / "assets",
            self.demo_dir / "reports",
            self.demo_dir / "screenshots",
            self.docs_dir,
            self.docs_dir / "api",
            self.docs_dir / "user_guides",
            self.docs_dir / "technical",
            self.data_dir,
            self.data_dir / "mock_data",
            self.data_dir / "templates"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ Created directory: {directory}")
    
    def install_dependencies(self):
        """Install required Python packages"""
        requirements = [
            "streamlit>=1.28.0",
            "plotly>=5.15.0",
            "pandas>=2.0.0",
            "numpy>=1.24.0",
            "python-dateutil>=2.8.0",
            "openpyxl>=3.1.0",
            "jinja2>=3.1.0",
            "markdown>=3.4.0",
            "pydantic>=2.0.0",
            "fastapi>=0.100.0",
            "uvicorn>=0.23.0"
        ]
        
        print("📦 Installing required packages...")
        for package in requirements:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ Installed: {package}")
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install {package}: {e}")
    
    def create_config_files(self):
        """Create configuration files for the demo"""
        
        # Streamlit config
        streamlit_config = """
[global]
developmentMode = false

[server]
port = 8501
enableCORS = false
enableXsrfProtection = false

[browser]
gatherUsageStats = false

[theme]
primaryColor = "#4ecdc4"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"
"""
        
        config_dir = self.project_root / ".streamlit"
        config_dir.mkdir(exist_ok=True)
        
        with open(config_dir / "config.toml", "w") as f:
            f.write(streamlit_config)
        
        # Demo configuration
        demo_config = {
            "app_name": "TicketSage AI ROI Calculator",
            "version": "1.0.0",
            "company": "TicketSage AI",
            "contact_email": "<EMAIL>",
            "demo_scenarios": {
                "msp": {
                    "name": "TechServe Solutions",
                    "description": "Regional MSP serving 200+ clients",
                    "default_params": {
                        "monthly_tickets": 15000,
                        "technicians": 50,
                        "avg_resolution_hours": 4.2,
                        "hourly_rate": 85
                    }
                },
                "healthcare": {
                    "name": "MedCare Network",
                    "description": "Regional health system with 5 hospitals",
                    "default_params": {
                        "monthly_tickets": 8500,
                        "facilities": 33,
                        "avg_resolution_hours": 2.8,
                        "hourly_rate": 45
                    }
                },
                "manufacturing": {
                    "name": "PrecisionTech Industries",
                    "description": "Advanced manufacturing with 500 employees",
                    "default_params": {
                        "monthly_tickets": 2200,
                        "employees": 500,
                        "avg_resolution_hours": 3.2,
                        "downtime_cost": 50000
                    }
                }
            }
        }
        
        with open(self.demo_dir / "config.json", "w") as f:
            json.dump(demo_config, f, indent=2)
        
        print("✅ Created configuration files")
    
    def generate_sample_data(self):
        """Generate sample data for all verticals"""
        print("📊 Generating sample data...")
        
        try:
            from mock_data_generator import IndustryMockDataGenerator, IndustryVertical
            
            verticals = [
                (IndustryVertical.MSP, "msp_sample_data.json"),
                (IndustryVertical.HEALTHCARE, "healthcare_sample_data.json"),
                (IndustryVertical.MANUFACTURING, "manufacturing_sample_data.json"),
                (IndustryVertical.FINANCIAL, "financial_sample_data.json"),
                (IndustryVertical.EDUCATION, "education_sample_data.json")
            ]
            
            for vertical, filename in verticals:
                generator = IndustryMockDataGenerator(vertical, "medium")
                tickets = generator.generate_historical_tickets(6)  # 6 months of data
                
                # Convert to serializable format
                sample_data = []
                for ticket in tickets[:100]:  # Limit to 100 tickets for demo
                    sample_data.append({
                        "id": ticket.id,
                        "category": ticket.category,
                        "subcategory": ticket.subcategory,
                        "priority": ticket.priority,
                        "description": ticket.description,
                        "created_date": ticket.created_date.isoformat(),
                        "resolved_date": ticket.resolved_date.isoformat(),
                        "resolution_time_hours": ticket.resolution_time_hours,
                        "cost": ticket.cost,
                        "sla_met": ticket.sla_met
                    })
                
                with open(self.data_dir / "mock_data" / filename, "w") as f:
                    json.dump(sample_data, f, indent=2)
                
                print(f"✅ Generated sample data for {vertical.value}")
        
        except ImportError:
            print("⚠️  Mock data generator not available. Please ensure mock_data_generator.py is in the project root.")
    
    def create_demo_launcher(self):
        """Create demo launcher script"""
        launcher_script = '''#!/usr/bin/env python3
"""
TicketSage AI Demo Launcher
Quick launcher for the ROI calculator demo
"""

import subprocess
import sys
import webbrowser
import time
from pathlib import Path

def main():
    print("🚀 Starting TicketSage AI Demo Environment...")
    
    # Check if streamlit is installed
    try:
        import streamlit
        print("✅ Streamlit is available")
    except ImportError:
        print("❌ Streamlit not found. Please run setup_demo_environment.py first")
        return
    
    # Start the Streamlit app
    try:
        print("📊 Launching ROI Calculator...")
        print("🌐 Demo will open in your browser at: http://localhost:8501")
        print("⏹️  Press Ctrl+C to stop the demo")
        
        # Launch Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "roi_calculator.py",
            "--server.port", "8501",
            "--server.headless", "true"
        ])
        
    except KeyboardInterrupt:
        print("\\n👋 Demo stopped. Thank you for using TicketSage AI!")
    except Exception as e:
        print(f"❌ Error starting demo: {e}")

if __name__ == "__main__":
    main()
'''
        
        with open(self.project_root / "launch_demo.py", "w") as f:
            f.write(launcher_script)
        
        # Make it executable on Unix systems
        if os.name != 'nt':
            os.chmod(self.project_root / "launch_demo.py", 0o755)
        
        print("✅ Created demo launcher script")
    
    def create_readme(self):
        """Create comprehensive README for the demo"""
        readme_content = '''# TicketSage AI Demo Environment

## Quick Start

1. **Setup Environment:**
   ```bash
   python setup_demo_environment.py
   ```

2. **Launch Demo:**
   ```bash
   python launch_demo.py
   ```

3. **Open Browser:**
   Navigate to http://localhost:8501

## Demo Components

### 📊 ROI Calculator (`roi_calculator.py`)
Interactive Streamlit application for demonstrating cost savings across industry verticals.

**Features:**
- Real-time ROI calculations
- Industry-specific parameters
- Visual cost comparisons
- Downloadable reports

### 🏭 Mock Data Generator (`mock_data_generator.py`)
Generates realistic ticket data for demonstration purposes.

**Supported Verticals:**
- Managed Service Providers (MSP)
- Healthcare Systems
- Manufacturing Plants
- Financial Institutions
- Educational Institutions

### 📋 Demo Script (`demo_script.md`)
Structured presentation guide for customer demonstrations.

## Directory Structure

```
TicketSageAutomation/
├── demo/                          # Demo assets and reports
│   ├── assets/                    # Images, logos, presentations
│   ├── reports/                   # Generated ROI reports
│   └── screenshots/               # Demo screenshots
├── docs/                          # Documentation
│   ├── api/                       # API documentation
│   ├── user_guides/               # User guides
│   └── technical/                 # Technical documentation
├── data/                          # Data files
│   ├── mock_data/                 # Generated sample data
│   └── templates/                 # Report templates
├── roi_calculator.py              # Main demo application
├── mock_data_generator.py         # Data generation utility
├── launch_demo.py                 # Demo launcher
└── setup_demo_environment.py     # Environment setup
```

## Usage Instructions

### For Sales Demonstrations

1. **Prepare the Demo:**
   - Review `demo_script.md`
   - Test ROI calculator with different scenarios
   - Prepare industry-specific talking points

2. **During the Demo:**
   - Start with MSP vertical (most relatable)
   - Show live ROI calculations
   - Customize parameters based on prospect's situation
   - Generate and download ROI report

3. **Follow-up:**
   - Provide customized ROI analysis
   - Schedule technical deep-dive
   - Propose pilot program

### For Technical Validation

1. **Review Architecture:**
   - Study `architecture_recommendations.md`
   - Understand multi-agent system design
   - Review technology stack choices

2. **Examine Implementation:**
   - Review `implementation_plan.md`
   - Understand phased approach
   - Assess technical requirements

## Customization

### Adding New Industry Verticals

1. **Update Mock Data Generator:**
   ```python
   # Add new vertical to IndustryVertical enum
   class IndustryVertical(Enum):
       NEW_VERTICAL = "new_vertical"
   
   # Add baseline metrics and ticket categories
   ```

2. **Update ROI Calculator:**
   ```python
   # Add industry configuration
   "New Industry": {
       "vertical": IndustryVertical.NEW_VERTICAL,
       "default_params": {...}
   }
   ```

### Customizing Branding

1. **Update Streamlit Theme:**
   Edit `.streamlit/config.toml`

2. **Add Company Logo:**
   Place logo in `demo/assets/` and update ROI calculator

3. **Customize Reports:**
   Modify report templates in `data/templates/`

## Troubleshooting

### Common Issues

**Streamlit won't start:**
- Check Python version (3.8+ required)
- Verify all dependencies installed
- Check port 8501 availability

**Mock data generation fails:**
- Ensure numpy and pandas are installed
- Check file permissions in data directory

**ROI calculations seem incorrect:**
- Verify industry-specific parameters
- Check baseline metrics in mock_data_generator.py

### Getting Help

- Review documentation in `docs/`
- Check demo script for guidance
- Contact: <EMAIL>

## Next Steps

1. **Schedule Custom Demo:** Using prospect's actual data
2. **Technical Deep Dive:** Architecture and integration review
3. **Pilot Program:** 30-day proof of concept
4. **Implementation Planning:** Detailed project roadmap
'''
        
        with open(self.project_root / "README_DEMO.md", "w") as f:
            f.write(readme_content)
        
        print("✅ Created comprehensive README")
    
    def run_setup(self):
        """Run complete setup process"""
        print("🚀 Setting up TicketSage AI Demo Environment...")
        print("=" * 50)
        
        self.setup_directories()
        self.install_dependencies()
        self.create_config_files()
        self.generate_sample_data()
        self.create_demo_launcher()
        self.create_readme()
        
        print("\n" + "=" * 50)
        print("✅ Demo environment setup complete!")
        print("\n📋 Next steps:")
        print("1. Run: python launch_demo.py")
        print("2. Open browser to: http://localhost:8501")
        print("3. Review demo_script.md for presentation guidance")
        print("4. Check README_DEMO.md for detailed instructions")

if __name__ == "__main__":
    setup = DemoEnvironmentSetup()
    setup.run_setup()

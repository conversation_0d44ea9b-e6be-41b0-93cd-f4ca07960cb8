# TicketSage AI Demo Environment Setup Script for Windows
# PowerShell script for Windows environments

Write-Host "🚀 Setting up TicketSage AI Demo Environment..." -ForegroundColor Green
Write-Host "=" * 50

# Check if Python is installed
function Test-PythonInstallation {
    try {
        $pythonVersion = & python --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
            return $true
        }
    }
    catch {
        # Try python3
        try {
            $pythonVersion = & python3 --version 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Python3 found: $pythonVersion" -ForegroundColor Green
                Set-Alias python python3
                return $true
            }
        }
        catch {
            Write-Host "❌ Python not found. Please install Python 3.8+ from https://python.org" -ForegroundColor Red
            return $false
        }
    }
    return $false
}

# Create directory structure
function New-DemoDirectories {
    Write-Host "📁 Creating directory structure..." -ForegroundColor Yellow
    
    $directories = @(
        "demo",
        "demo\assets",
        "demo\reports", 
        "demo\screenshots",
        "docs",
        "docs\api",
        "docs\user_guides",
        "docs\technical",
        "data",
        "data\mock_data",
        "data\templates"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Host "✅ Created directory: $dir" -ForegroundColor Green
        }
    }
}

# Install Python packages
function Install-PythonPackages {
    Write-Host "📦 Installing Python packages..." -ForegroundColor Yellow
    
    if (Test-Path "requirements.txt") {
        try {
            & python -m pip install --upgrade pip
            & python -m pip install -r requirements.txt
            Write-Host "✅ Python packages installed successfully" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ Failed to install Python packages. Error: $_" -ForegroundColor Red
            return $false
        }
    }
    else {
        Write-Host "⚠️  requirements.txt not found. Installing core packages..." -ForegroundColor Yellow
        $corePackages = @(
            "streamlit>=1.28.0",
            "plotly>=5.15.0", 
            "pandas>=2.0.0",
            "numpy>=1.24.0"
        )
        
        foreach ($package in $corePackages) {
            try {
                & python -m pip install $package
                Write-Host "✅ Installed: $package" -ForegroundColor Green
            }
            catch {
                Write-Host "❌ Failed to install: $package" -ForegroundColor Red
            }
        }
    }
    return $true
}

# Create Streamlit configuration
function New-StreamlitConfig {
    Write-Host "⚙️  Creating Streamlit configuration..." -ForegroundColor Yellow
    
    $streamlitDir = ".streamlit"
    if (!(Test-Path $streamlitDir)) {
        New-Item -ItemType Directory -Path $streamlitDir -Force | Out-Null
    }
    
    $configContent = @"
[global]
developmentMode = false

[server]
port = 8501
enableCORS = false
enableXsrfProtection = false

[browser]
gatherUsageStats = false

[theme]
primaryColor = "#4ecdc4"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"
"@
    
    $configContent | Out-File -FilePath "$streamlitDir\config.toml" -Encoding UTF8
    Write-Host "✅ Streamlit configuration created" -ForegroundColor Green
}

# Create demo configuration
function New-DemoConfig {
    Write-Host "⚙️  Creating demo configuration..." -ForegroundColor Yellow
    
    $demoConfig = @{
        app_name = "TicketSage AI ROI Calculator"
        version = "1.0.0"
        company = "TicketSage AI"
        contact_email = "<EMAIL>"
        demo_scenarios = @{
            msp = @{
                name = "TechServe Solutions"
                description = "Regional MSP serving 200+ clients"
                default_params = @{
                    monthly_tickets = 15000
                    technicians = 50
                    avg_resolution_hours = 4.2
                    hourly_rate = 85
                }
            }
            healthcare = @{
                name = "MedCare Network"
                description = "Regional health system with 5 hospitals"
                default_params = @{
                    monthly_tickets = 8500
                    facilities = 33
                    avg_resolution_hours = 2.8
                    hourly_rate = 45
                }
            }
        }
    }
    
    $demoConfig | ConvertTo-Json -Depth 4 | Out-File -FilePath "demo\config.json" -Encoding UTF8
    Write-Host "✅ Demo configuration created" -ForegroundColor Green
}

# Create launcher script
function New-LauncherScript {
    Write-Host "🚀 Creating demo launcher..." -ForegroundColor Yellow
    
    $launcherContent = @'
"""
TicketSage AI Demo Launcher for Windows
Quick launcher for the ROI calculator demo
"""

import subprocess
import sys
import webbrowser
import time
import os
from pathlib import Path

def main():
    print("🚀 Starting TicketSage AI Demo Environment...")
    
    # Check if streamlit is installed
    try:
        import streamlit
        print("✅ Streamlit is available")
    except ImportError:
        print("❌ Streamlit not found. Please run setup_demo_windows.ps1 first")
        return
    
    # Check if ROI calculator exists
    if not Path("roi_calculator.py").exists():
        print("❌ ROI calculator not found. Please ensure roi_calculator.py is in the current directory")
        return
    
    # Start the Streamlit app
    try:
        print("📊 Launching ROI Calculator...")
        print("🌐 Demo will open in your browser at: http://localhost:8501")
        print("⏹️  Press Ctrl+C to stop the demo")
        
        # Launch Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "roi_calculator.py",
            "--server.port", "8501",
            "--server.headless", "true"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 Demo stopped. Thank you for using TicketSage AI!")
    except Exception as e:
        print(f"❌ Error starting demo: {e}")

if __name__ == "__main__":
    main()
'@
    
    $launcherContent | Out-File -FilePath "launch_demo.py" -Encoding UTF8
    Write-Host "✅ Demo launcher created" -ForegroundColor Green
}

# Generate sample data (simplified version)
function New-SampleData {
    Write-Host "📊 Creating sample data..." -ForegroundColor Yellow
    
    $sampleData = @{
        msp_tickets = @(
            @{
                id = "TKT-2024-001"
                category = "Password Reset"
                priority = "Medium"
                resolution_time = 0.5
                cost = 42.50
                automated = $true
            },
            @{
                id = "TKT-2024-002"
                category = "Software Installation"
                priority = "Low"
                resolution_time = 2.0
                cost = 170.00
                automated = $false
            }
        )
        healthcare_tickets = @(
            @{
                id = "TKT-2024-003"
                category = "Patient Registration"
                priority = "High"
                resolution_time = 1.5
                cost = 67.50
                automated = $true
            }
        )
    }
    
    $sampleData | ConvertTo-Json -Depth 3 | Out-File -FilePath "data\mock_data\sample_tickets.json" -Encoding UTF8
    Write-Host "✅ Sample data created" -ForegroundColor Green
}

# Create README
function New-ReadmeFile {
    Write-Host "📝 Creating README..." -ForegroundColor Yellow
    
    $readmeContent = @"
# TicketSage AI Demo Environment - Windows Setup

## Quick Start

1. **Setup completed!** ✅
2. **Launch Demo:**
   ``````
   python launch_demo.py
   ``````
3. **Open Browser:** Navigate to http://localhost:8501

## What's Available

- **ROI Calculator**: Interactive Streamlit application
- **Mock Data**: Sample ticket data for demonstrations
- **Documentation**: Complete guides in docs/ directory
- **Demo Script**: Presentation guide in demo_script.md

## Troubleshooting

**Demo won't start:**
- Ensure Python 3.8+ is installed
- Run: ``python -m pip install streamlit plotly pandas numpy``
- Check port 8501 is available

**Need Help:**
- Check docs/user_guide_complete.md
- Review demo_script.md for presentation guidance
- Contact: <EMAIL>

## Next Steps

1. Practice with the ROI calculator
2. Review demo_script.md for presentation guidance
3. Customize parameters for your prospects
4. Generate ROI reports for follow-up

Ready to demonstrate the future of intelligent automation!
"@
    
    $readmeContent | Out-File -FilePath "README_WINDOWS.md" -Encoding UTF8
    Write-Host "✅ README created" -ForegroundColor Green
}

# Main setup function
function Start-DemoSetup {
    # Check Python installation
    if (!(Test-PythonInstallation)) {
        Write-Host "❌ Setup failed: Python not found" -ForegroundColor Red
        Write-Host "Please install Python 3.8+ from https://python.org and try again" -ForegroundColor Yellow
        return
    }
    
    # Create directories
    New-DemoDirectories
    
    # Install packages
    if (!(Install-PythonPackages)) {
        Write-Host "⚠️  Package installation had issues, but continuing..." -ForegroundColor Yellow
    }
    
    # Create configuration files
    New-StreamlitConfig
    New-DemoConfig
    New-LauncherScript
    New-SampleData
    New-ReadmeFile
    
    Write-Host ""
    Write-Host "=" * 50
    Write-Host "✅ Demo environment setup complete!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Next steps:" -ForegroundColor Yellow
    Write-Host "1. Run: python launch_demo.py" -ForegroundColor White
    Write-Host "2. Open browser to: http://localhost:8501" -ForegroundColor White
    Write-Host "3. Review demo_script.md for presentation guidance" -ForegroundColor White
    Write-Host "4. Check README_WINDOWS.md for detailed instructions" -ForegroundColor White
    Write-Host ""
    Write-Host "🎯 Ready to demonstrate TicketSage AI!" -ForegroundColor Green
}

# Run the setup
Start-DemoSetup

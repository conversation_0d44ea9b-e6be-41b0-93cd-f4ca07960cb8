# TicketSage AI Implementation Plan

## Phase 1: Foundation Setup (Weeks 1-8)

### Week 1-2: Infrastructure & Development Environment

#### Day 1-3: Project Setup
```bash
# Create project structure
mkdir ticketsage-ai
cd ticketsage-ai

# Initialize Python project with Poetry
poetry init
poetry add fastapi uvicorn langchain langchain-community
poetry add pytorch torchvision torchaudio
poetry add qdrant-client redis kafka-python
poetry add sqlalchemy alembic psycopg2-binary
poetry add prometheus-client structlog

# Create directory structure
mkdir -p {
    src/agents/{orchestrator,triage,resolution,knowledge,sla_guardian,opportunity_scout},
    src/integrations/{autotask,itglue,hubspot,ninjaone},
    src/models/{sla_prediction,knowledge_extraction,opportunity_scoring},
    src/api/{routes,middleware,schemas},
    src/core/{config,database,messaging,security},
    infrastructure/{kubernetes,helm,terraform},
    tests/{unit,integration,e2e},
    docs/{api,architecture,deployment}
}
```

#### Day 4-5: Kubernetes Infrastructure
```yaml
# infrastructure/kubernetes/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ticketsage-ai
  labels:
    istio-injection: enabled

---
# infrastructure/kubernetes/kafka.yaml
apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: ticketsage-kafka
  namespace: ticketsage-ai
spec:
  kafka:
    version: 3.6.0
    replicas: 3
    listeners:
      - name: plain
        port: 9092
        type: internal
        tls: false
      - name: tls
        port: 9093
        type: internal
        tls: true
    config:
      offsets.topic.replication.factor: 3
      transaction.state.log.replication.factor: 3
      transaction.state.log.min.isr: 2
    storage:
      type: jbod
      volumes:
      - id: 0
        type: persistent-claim
        size: 100Gi
        deleteClaim: false
  zookeeper:
    replicas: 3
    storage:
      type: persistent-claim
      size: 10Gi
      deleteClaim: false
```

### Week 3-4: Core Agent Framework

#### Master Orchestrator Implementation
```python
# src/agents/orchestrator/master_orchestrator.py
from typing import List, Dict, Any
import asyncio
from dataclasses import dataclass
from enum import Enum

class AgentType(Enum):
    TRIAGE = "triage"
    RESOLUTION = "resolution"
    KNOWLEDGE = "knowledge"
    SLA_GUARDIAN = "sla_guardian"
    OPPORTUNITY_SCOUT = "opportunity_scout"

@dataclass
class AgentTask:
    agent_type: AgentType
    ticket_id: str
    priority: int
    payload: Dict[str, Any]
    correlation_id: str

class MasterOrchestrator:
    def __init__(self):
        self.agent_registry = AgentRegistry()
        self.task_queue = TaskQueue()
        self.conflict_resolver = ConflictResolver()
        self.performance_monitor = PerformanceMonitor()
    
    async def process_ticket(self, ticket: Ticket) -> TicketResolution:
        """Main orchestration logic for ticket processing"""
        correlation_id = self.generate_correlation_id()
        
        # Determine required agents based on ticket characteristics
        required_agents = self.determine_required_agents(ticket)
        
        # Create tasks for each agent
        tasks = []
        for agent_type in required_agents:
            task = AgentTask(
                agent_type=agent_type,
                ticket_id=ticket.id,
                priority=ticket.priority,
                payload=ticket.to_dict(),
                correlation_id=correlation_id
            )
            tasks.append(self.delegate_to_agent(task))
        
        # Execute tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Resolve conflicts and create final resolution
        final_resolution = await self.conflict_resolver.resolve(
            results, correlation_id
        )
        
        # Update performance metrics
        await self.performance_monitor.record_resolution(
            ticket, final_resolution, correlation_id
        )
        
        return final_resolution
    
    def determine_required_agents(self, ticket: Ticket) -> List[AgentType]:
        """Determine which agents are needed for this ticket"""
        agents = [AgentType.TRIAGE]  # Always start with triage
        
        # Add SLA Guardian if ticket has SLA requirements
        if ticket.sla_deadline:
            agents.append(AgentType.SLA_GUARDIAN)
        
        # Add Resolution agent for automated resolution attempts
        if ticket.category in self.get_automatable_categories():
            agents.append(AgentType.RESOLUTION)
        
        # Add Knowledge agent for knowledge extraction/application
        agents.append(AgentType.KNOWLEDGE)
        
        # Add Opportunity Scout for revenue opportunity identification
        if ticket.client_tier in ['enterprise', 'premium']:
            agents.append(AgentType.OPPORTUNITY_SCOUT)
        
        return agents
```

#### Agent Communication Protocol
```python
# src/core/messaging/agent_messaging.py
import json
from typing import Optional, Dict, Any
from kafka import KafkaProducer, KafkaConsumer
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class AgentMessage:
    sender: str
    recipient: str
    message_type: str
    payload: Dict[str, Any]
    correlation_id: str
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

class AgentMessaging:
    def __init__(self, kafka_bootstrap_servers: str):
        self.producer = KafkaProducer(
            bootstrap_servers=kafka_bootstrap_servers,
            value_serializer=lambda v: json.dumps(v).encode('utf-8')
        )
        self.consumers = {}
    
    async def send_message(self, message: AgentMessage):
        """Send message to specific agent"""
        topic = f"agent-{message.recipient}"
        await self.producer.send(topic, asdict(message))
    
    async def broadcast_message(self, message: AgentMessage):
        """Broadcast message to all agents"""
        topic = "agent-broadcast"
        await self.producer.send(topic, asdict(message))
    
    def create_consumer(self, agent_name: str) -> KafkaConsumer:
        """Create consumer for specific agent"""
        topic = f"agent-{agent_name}"
        consumer = KafkaConsumer(
            topic,
            bootstrap_servers=self.kafka_bootstrap_servers,
            value_deserializer=lambda m: json.loads(m.decode('utf-8')),
            group_id=f"{agent_name}-group"
        )
        self.consumers[agent_name] = consumer
        return consumer
```

### Week 5-6: Knowledge Management System

#### Vector Database Integration
```python
# src/core/knowledge/vector_store.py
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
from typing import List, Dict, Any, Optional
import numpy as np

class KnowledgeVectorStore:
    def __init__(self, qdrant_url: str, collection_name: str = "knowledge_base"):
        self.client = QdrantClient(url=qdrant_url)
        self.collection_name = collection_name
        self.setup_collection()
    
    def setup_collection(self):
        """Initialize the knowledge collection"""
        try:
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=768,  # BERT embedding size
                    distance=Distance.COSINE
                )
            )
        except Exception as e:
            # Collection might already exist
            pass
    
    async def store_knowledge(self, knowledge_id: str, embedding: np.ndarray, 
                            metadata: Dict[str, Any]):
        """Store knowledge with embedding and metadata"""
        point = PointStruct(
            id=knowledge_id,
            vector=embedding.tolist(),
            payload=metadata
        )
        
        self.client.upsert(
            collection_name=self.collection_name,
            points=[point]
        )
    
    async def search_similar_knowledge(self, query_embedding: np.ndarray, 
                                     limit: int = 5) -> List[Dict[str, Any]]:
        """Search for similar knowledge based on embedding"""
        search_result = self.client.search(
            collection_name=self.collection_name,
            query_vector=query_embedding.tolist(),
            limit=limit,
            with_payload=True
        )
        
        return [
            {
                "id": hit.id,
                "score": hit.score,
                "metadata": hit.payload
            }
            for hit in search_result
        ]
```

#### Knowledge Extraction Agent
```python
# src/agents/knowledge/knowledge_agent.py
from transformers import AutoTokenizer, AutoModel
import torch
from typing import List, Dict, Any
import re

class KnowledgeAgent:
    def __init__(self, vector_store: KnowledgeVectorStore):
        self.vector_store = vector_store
        self.tokenizer = AutoTokenizer.from_pretrained('sentence-transformers/all-MiniLM-L6-v2')
        self.model = AutoModel.from_pretrained('sentence-transformers/all-MiniLM-L6-v2')
        self.knowledge_extractor = KnowledgeExtractor()
    
    async def extract_knowledge_from_resolution(self, ticket: Ticket, 
                                              resolution: TicketResolution):
        """Extract actionable knowledge from successful ticket resolution"""
        # Extract resolution steps
        steps = self.knowledge_extractor.extract_steps(resolution.description)
        
        # Create knowledge document
        knowledge_doc = {
            "title": f"Resolution for {ticket.category} - {ticket.subcategory}",
            "steps": steps,
            "ticket_type": ticket.category,
            "domain": ticket.domain,
            "success_rate": 1.0,
            "created_from_ticket": ticket.id,
            "resolution_time": resolution.resolution_time,
            "technician_feedback": resolution.technician_feedback
        }
        
        # Generate embedding
        embedding = await self.generate_embedding(
            f"{knowledge_doc['title']} {' '.join(steps)}"
        )
        
        # Store in vector database
        knowledge_id = f"knowledge_{ticket.id}_{resolution.id}"
        await self.vector_store.store_knowledge(
            knowledge_id, embedding, knowledge_doc
        )
        
        return knowledge_id
    
    async def find_relevant_knowledge(self, ticket: Ticket) -> List[Dict[str, Any]]:
        """Find relevant knowledge for incoming ticket"""
        # Create search query from ticket
        search_query = f"{ticket.category} {ticket.subcategory} {ticket.description}"
        
        # Generate embedding for search
        query_embedding = await self.generate_embedding(search_query)
        
        # Search for similar knowledge
        similar_knowledge = await self.vector_store.search_similar_knowledge(
            query_embedding, limit=5
        )
        
        # Filter by relevance threshold
        relevant_knowledge = [
            knowledge for knowledge in similar_knowledge
            if knowledge["score"] > 0.7
        ]
        
        return relevant_knowledge
    
    async def generate_embedding(self, text: str) -> torch.Tensor:
        """Generate embedding for text using sentence transformer"""
        inputs = self.tokenizer(text, return_tensors="pt", truncation=True, 
                               padding=True, max_length=512)
        
        with torch.no_grad():
            outputs = self.model(**inputs)
            # Use mean pooling
            embeddings = outputs.last_hidden_state.mean(dim=1)
        
        return embeddings.squeeze()

class KnowledgeExtractor:
    def extract_steps(self, resolution_text: str) -> List[str]:
        """Extract actionable steps from resolution description"""
        # Simple regex-based extraction (can be enhanced with NLP)
        step_patterns = [
            r'(?:Step \d+[:.]\s*)(.+?)(?=Step \d+|$)',
            r'(?:\d+[.)]\s*)(.+?)(?=\d+[.)]|$)',
            r'(?:First|Then|Next|Finally)[,:]?\s*(.+?)(?=First|Then|Next|Finally|$)'
        ]
        
        steps = []
        for pattern in step_patterns:
            matches = re.findall(pattern, resolution_text, re.IGNORECASE | re.DOTALL)
            if matches:
                steps.extend([step.strip() for step in matches])
                break
        
        # If no structured steps found, split by sentences
        if not steps:
            sentences = resolution_text.split('.')
            steps = [sentence.strip() for sentence in sentences if len(sentence.strip()) > 10]
        
        return steps[:10]  # Limit to 10 steps
```

### Week 7-8: API Gateway & Security

#### FastAPI Application Structure
```python
# src/api/main.py
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import structlog
from prometheus_fastapi_instrumentator import Instrumentator

from .routes import tickets, agents, knowledge, analytics
from .middleware.auth import verify_token
from .middleware.logging import LoggingMiddleware
from .middleware.rate_limiting import RateLimitingMiddleware

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

app = FastAPI(
    title="TicketSage AI API",
    description="Multi-Agent AI Platform for Intelligent Ticket Management",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Security middleware
security = HTTPBearer()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://app.ticketsage.ai", "https://admin.ticketsage.ai"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Trusted host middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["ticketsage.ai", "*.ticketsage.ai", "localhost"]
)

# Custom middleware
app.add_middleware(LoggingMiddleware)
app.add_middleware(RateLimitingMiddleware)

# Prometheus metrics
Instrumentator().instrument(app).expose(app)

# Include routers
app.include_router(tickets.router, prefix="/api/v1/tickets", tags=["tickets"])
app.include_router(agents.router, prefix="/api/v1/agents", tags=["agents"])
app.include_router(knowledge.router, prefix="/api/v1/knowledge", tags=["knowledge"])
app.include_router(analytics.router, prefix="/api/v1/analytics", tags=["analytics"])

@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0"}

@app.get("/ready")
async def readiness_check():
    # Check dependencies (database, kafka, redis)
    return {"status": "ready"}
```

#### Authentication & Authorization
```python
# src/api/middleware/auth.py
from fastapi import HTTPException, status, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt
from typing import Optional, List
import redis
from datetime import datetime, timedelta

security = HTTPBearer()

class AuthService:
    def __init__(self, redis_client: redis.Redis, jwt_secret: str):
        self.redis_client = redis_client
        self.jwt_secret = jwt_secret
        self.algorithm = "HS256"
    
    def create_access_token(self, user_id: str, roles: List[str], 
                          expires_delta: Optional[timedelta] = None):
        """Create JWT access token"""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(hours=24)
        
        to_encode = {
            "sub": user_id,
            "roles": roles,
            "exp": expire,
            "iat": datetime.utcnow()
        }
        
        encoded_jwt = jwt.encode(to_encode, self.jwt_secret, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> dict:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.algorithm])
            
            # Check if token is blacklisted
            if self.redis_client.get(f"blacklist:{token}"):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has been revoked"
                )
            
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials"
            )

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Dependency to verify JWT token"""
    auth_service = AuthService(redis_client, jwt_secret)  # Inject dependencies
    payload = auth_service.verify_token(credentials.credentials)
    return payload

def require_roles(required_roles: List[str]):
    """Decorator to require specific roles"""
    def role_checker(token_payload: dict = Depends(verify_token)):
        user_roles = token_payload.get("roles", [])
        if not any(role in user_roles for role in required_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return token_payload
    return role_checker
```

## Next Steps

This implementation plan provides a solid foundation for the first 8 weeks. The next phases will focus on:

1. **ML Model Development** - SLA prediction, knowledge extraction, opportunity scoring
2. **Integration Connectors** - Autotask, IT Glue, HubSpot, NinjaOne
3. **Domain-Specific Agents** - Healthcare, Manufacturing, Financial Services
4. **Advanced Features** - Voice interface, advanced analytics, cross-domain capabilities

Each component is designed to be modular, scalable, and aligned with your patent strategy while delivering the business value outlined in your proposal.

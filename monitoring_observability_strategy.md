# TicketSage AI - Monitoring & Observability Strategy

## Observability Architecture Overview

The TicketSage AI platform implements comprehensive observability using the three pillars: metrics, logs, and traces. This enables proactive monitoring, rapid troubleshooting, and continuous optimization of system performance.

## Monitoring Stack

### **Metrics Collection: Prometheus + Grafana**
```yaml
# Prometheus Configuration
global:
  scrape_interval: 30s
  evaluation_interval: 30s

rule_files:
  - "ticketsage_alerts.yml"
  - "sla_rules.yml"

scrape_configs:
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)

  - job_name: 'ticketsage-orchestrator'
    static_configs:
      - targets: ['orchestrator-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'ticketsage-agents'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names: ['ticketsage-production']
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_label_component]
        action: keep
        regex: agent
```

### **Custom Application Metrics**
```python
# src/core/metrics.py
from prometheus_client import Counter, Histogram, Gauge, Info
import time
from functools import wraps

# Business Metrics
ticket_processing_total = Counter(
    'ticketsage_tickets_processed_total',
    'Total number of tickets processed',
    ['agent_type', 'category', 'status', 'client_id']
)

ticket_processing_duration = Histogram(
    'ticketsage_ticket_processing_duration_seconds',
    'Time spent processing tickets',
    ['agent_type', 'category', 'priority'],
    buckets=[0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0, 300.0, 600.0]
)

sla_compliance_rate = Gauge(
    'ticketsage_sla_compliance_rate',
    'SLA compliance rate by client and category',
    ['client_id', 'category']
)

agent_performance_score = Gauge(
    'ticketsage_agent_performance_score',
    'Agent performance score',
    ['agent_id', 'agent_type']
)

automation_rate = Gauge(
    'ticketsage_automation_rate',
    'Percentage of tickets automated',
    ['client_id', 'category']
)

cost_savings = Counter(
    'ticketsage_cost_savings_total',
    'Total cost savings in USD',
    ['client_id', 'category']
)

# System Metrics
active_workflows = Gauge(
    'ticketsage_active_workflows',
    'Number of active workflows'
)

agent_load = Gauge(
    'ticketsage_agent_load',
    'Current load per agent',
    ['agent_id', 'agent_type']
)

knowledge_base_size = Gauge(
    'ticketsage_knowledge_entries_total',
    'Total number of knowledge entries',
    ['domain', 'category']
)

# Decorator for automatic metrics collection
def track_processing_time(agent_type: str, category: str = None):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                status = 'success'
                return result
            except Exception as e:
                status = 'error'
                raise
            finally:
                duration = time.time() - start_time
                ticket_processing_duration.labels(
                    agent_type=agent_type,
                    category=category or 'unknown',
                    priority=kwargs.get('priority', 'unknown')
                ).observe(duration)
                
                ticket_processing_total.labels(
                    agent_type=agent_type,
                    category=category or 'unknown',
                    status=status,
                    client_id=kwargs.get('client_id', 'unknown')
                ).inc()
        return wrapper
    return decorator
```

### **Grafana Dashboards**
```json
{
  "dashboard": {
    "title": "TicketSage AI - Business Metrics",
    "panels": [
      {
        "title": "Ticket Processing Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(ticketsage_tickets_processed_total[5m])",
            "legendFormat": "Tickets/sec"
          }
        ]
      },
      {
        "title": "SLA Compliance by Client",
        "type": "table",
        "targets": [
          {
            "expr": "ticketsage_sla_compliance_rate",
            "format": "table"
          }
        ]
      },
      {
        "title": "Automation Rate Trend",
        "type": "timeseries",
        "targets": [
          {
            "expr": "avg(ticketsage_automation_rate) by (client_id)",
            "legendFormat": "{{client_id}}"
          }
        ]
      },
      {
        "title": "Cost Savings by Category",
        "type": "piechart",
        "targets": [
          {
            "expr": "sum(rate(ticketsage_cost_savings_total[1h])) by (category)",
            "legendFormat": "{{category}}"
          }
        ]
      },
      {
        "title": "Agent Performance Heatmap",
        "type": "heatmap",
        "targets": [
          {
            "expr": "ticketsage_agent_performance_score",
            "legendFormat": "{{agent_id}}"
          }
        ]
      }
    ]
  }
}
```

### **Alerting Rules**
```yaml
# prometheus/ticketsage_alerts.yml
groups:
- name: ticketsage.business
  rules:
  - alert: SLAComplianceBelow80Percent
    expr: ticketsage_sla_compliance_rate < 0.8
    for: 10m
    labels:
      severity: critical
      team: operations
    annotations:
      summary: "SLA compliance below 80% for client {{ $labels.client_id }}"
      description: "Client {{ $labels.client_id }} has SLA compliance of {{ $value }}% for category {{ $labels.category }}"
      runbook_url: "https://docs.ticketsage.ai/runbooks/sla-compliance"

  - alert: HighTicketProcessingTime
    expr: histogram_quantile(0.95, rate(ticketsage_ticket_processing_duration_seconds_bucket[5m])) > 300
    for: 5m
    labels:
      severity: warning
      team: engineering
    annotations:
      summary: "High ticket processing time detected"
      description: "95th percentile processing time is {{ $value }} seconds for {{ $labels.agent_type }}"

  - alert: AgentPerformanceDropped
    expr: ticketsage_agent_performance_score < 0.7
    for: 15m
    labels:
      severity: warning
      team: engineering
    annotations:
      summary: "Agent performance below threshold"
      description: "Agent {{ $labels.agent_id }} performance score is {{ $value }}"

  - alert: AutomationRateDropped
    expr: ticketsage_automation_rate < 0.2
    for: 30m
    labels:
      severity: warning
      team: operations
    annotations:
      summary: "Automation rate below 20%"
      description: "Automation rate for client {{ $labels.client_id }} is {{ $value }}%"

- name: ticketsage.system
  rules:
  - alert: HighWorkflowBacklog
    expr: ticketsage_active_workflows > 1000
    for: 5m
    labels:
      severity: critical
      team: engineering
    annotations:
      summary: "High number of active workflows"
      description: "{{ $value }} active workflows detected, potential bottleneck"

  - alert: AgentUnresponsive
    expr: up{job="ticketsage-agents"} == 0
    for: 1m
    labels:
      severity: critical
      team: engineering
    annotations:
      summary: "Agent is unresponsive"
      description: "Agent {{ $labels.instance }} has been down for more than 1 minute"
```

## Logging Strategy

### **Structured Logging Configuration**
```python
# src/core/logging.py
import structlog
import logging
from pythonjsonlogger import jsonlogger

def configure_logging():
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Configure standard logging
    logHandler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter(
        fmt='%(asctime)s %(name)s %(levelname)s %(message)s'
    )
    logHandler.setFormatter(formatter)
    logger = logging.getLogger()
    logger.addHandler(logHandler)
    logger.setLevel(logging.INFO)

# Usage in application code
logger = structlog.get_logger()

@track_processing_time("triage", "password_reset")
async def process_ticket(ticket_id: str, client_id: str):
    logger.info(
        "Processing ticket",
        ticket_id=ticket_id,
        client_id=client_id,
        agent_type="triage"
    )
    
    try:
        result = await triage_logic(ticket_id)
        logger.info(
            "Ticket processed successfully",
            ticket_id=ticket_id,
            result=result,
            processing_time=result.processing_time
        )
        return result
    except Exception as e:
        logger.error(
            "Ticket processing failed",
            ticket_id=ticket_id,
            error=str(e),
            exc_info=True
        )
        raise
```

### **Log Aggregation with Fluent Bit**
```yaml
# fluent-bit-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush         1
        Log_Level     info
        Daemon        off
        Parsers_File  parsers.conf

    [INPUT]
        Name              tail
        Path              /var/log/containers/*ticketsage*.log
        Parser            docker
        Tag               kube.*
        Refresh_Interval  5
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On

    [FILTER]
        Name                kubernetes
        Match               kube.*
        Kube_URL            https://kubernetes.default.svc:443
        Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token
        Merge_Log           On
        K8S-Logging.Parser  On
        K8S-Logging.Exclude Off

    [FILTER]
        Name    grep
        Match   kube.*
        Regex   kubernetes.labels.app ticketsage

    [OUTPUT]
        Name            cloudwatch_logs
        Match           kube.*
        region          us-west-2
        log_group_name  /aws/eks/ticketsage/application
        log_stream_prefix from-fluent-bit-
        auto_create_group true
```

## Distributed Tracing

### **OpenTelemetry Configuration**
```python
# src/core/tracing.py
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.kafka import KafkaInstrumentor

def configure_tracing():
    # Configure tracer provider
    trace.set_tracer_provider(TracerProvider())
    tracer = trace.get_tracer(__name__)

    # Configure Jaeger exporter
    jaeger_exporter = JaegerExporter(
        agent_host_name="jaeger-agent",
        agent_port=6831,
    )

    # Configure span processor
    span_processor = BatchSpanProcessor(jaeger_exporter)
    trace.get_tracer_provider().add_span_processor(span_processor)

    return tracer

# Instrument frameworks
def instrument_app(app):
    FastAPIInstrumentor.instrument_app(app)
    SQLAlchemyInstrumentor().instrument()
    RedisInstrumentor().instrument()
    KafkaInstrumentor().instrument()

# Custom tracing for business logic
tracer = configure_tracing()

async def process_ticket_with_tracing(ticket_id: str):
    with tracer.start_as_current_span("process_ticket") as span:
        span.set_attribute("ticket.id", ticket_id)
        span.set_attribute("service.name", "orchestrator")
        
        with tracer.start_as_current_span("triage_analysis") as triage_span:
            triage_result = await triage_agent.analyze(ticket_id)
            triage_span.set_attribute("triage.category", triage_result.category)
            triage_span.set_attribute("triage.confidence", triage_result.confidence)
        
        with tracer.start_as_current_span("resolution_attempt") as resolution_span:
            resolution_result = await resolution_agent.resolve(ticket_id, triage_result)
            resolution_span.set_attribute("resolution.success", resolution_result.success)
            resolution_span.set_attribute("resolution.method", resolution_result.method)
        
        span.set_attribute("processing.duration", time.time() - start_time)
        return resolution_result
```

## Health Checks and SLIs/SLOs

### **Service Health Checks**
```python
# src/core/health.py
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import asyncio
import time

router = APIRouter()

class HealthChecker:
    def __init__(self):
        self.checks = {
            "database": self.check_database,
            "redis": self.check_redis,
            "kafka": self.check_kafka,
            "vector_db": self.check_vector_db,
            "agents": self.check_agents
        }
    
    async def check_database(self) -> Dict[str, Any]:
        try:
            start_time = time.time()
            # Simple database query
            result = await db.execute("SELECT 1")
            duration = time.time() - start_time
            return {
                "status": "healthy",
                "response_time": duration,
                "details": "Database connection successful"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def check_redis(self) -> Dict[str, Any]:
        try:
            start_time = time.time()
            await redis_client.ping()
            duration = time.time() - start_time
            return {
                "status": "healthy",
                "response_time": duration,
                "details": "Redis connection successful"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def run_all_checks(self) -> Dict[str, Any]:
        results = {}
        overall_healthy = True
        
        for check_name, check_func in self.checks.items():
            try:
                result = await asyncio.wait_for(check_func(), timeout=5.0)
                results[check_name] = result
                if result["status"] != "healthy":
                    overall_healthy = False
            except asyncio.TimeoutError:
                results[check_name] = {
                    "status": "unhealthy",
                    "error": "Health check timeout"
                }
                overall_healthy = False
            except Exception as e:
                results[check_name] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                overall_healthy = False
        
        return {
            "status": "healthy" if overall_healthy else "unhealthy",
            "timestamp": time.time(),
            "checks": results
        }

health_checker = HealthChecker()

@router.get("/health")
async def health_check():
    result = await health_checker.run_all_checks()
    if result["status"] == "unhealthy":
        raise HTTPException(status_code=503, detail=result)
    return result

@router.get("/ready")
async def readiness_check():
    # Simplified readiness check
    critical_checks = ["database", "redis", "kafka"]
    results = {}
    
    for check_name in critical_checks:
        check_func = health_checker.checks[check_name]
        result = await check_func()
        results[check_name] = result
        if result["status"] != "healthy":
            raise HTTPException(status_code=503, detail=results)
    
    return {"status": "ready", "checks": results}
```

### **SLI/SLO Definitions**
```yaml
# SLI/SLO Configuration
slis:
  ticket_processing_latency:
    description: "Time to process a ticket from creation to resolution"
    measurement: "histogram_quantile(0.95, rate(ticketsage_ticket_processing_duration_seconds_bucket[5m]))"
    unit: "seconds"
  
  sla_compliance_rate:
    description: "Percentage of tickets meeting SLA deadlines"
    measurement: "avg(ticketsage_sla_compliance_rate)"
    unit: "percentage"
  
  system_availability:
    description: "Percentage of time system is available"
    measurement: "avg(up{job=~'ticketsage.*'})"
    unit: "percentage"
  
  automation_success_rate:
    description: "Percentage of tickets successfully automated"
    measurement: "rate(ticketsage_tickets_processed_total{status='success'}[5m]) / rate(ticketsage_tickets_processed_total[5m])"
    unit: "percentage"

slos:
  ticket_processing_latency:
    target: "< 60 seconds for 95% of tickets"
    measurement_window: "30 days"
    
  sla_compliance_rate:
    target: "> 95% compliance rate"
    measurement_window: "30 days"
    
  system_availability:
    target: "> 99.9% uptime"
    measurement_window: "30 days"
    
  automation_success_rate:
    target: "> 90% success rate"
    measurement_window: "7 days"
```

This comprehensive monitoring and observability strategy ensures that TicketSage AI maintains high performance, reliability, and provides clear visibility into both business and technical metrics.

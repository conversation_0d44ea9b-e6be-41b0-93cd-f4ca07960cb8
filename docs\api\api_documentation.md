# TicketSage AI API Documentation

## Overview

The TicketSage AI API provides programmatic access to all platform capabilities including ticket processing, agent management, knowledge retrieval, and analytics. The API follows REST principles and uses JSON for data exchange.

**Base URL**: `https://api.ticketsage.ai/v1`
**Authentication**: Bearer token (JWT)
**Rate Limiting**: 1000 requests per minute per API key

## Authentication

### Obtaining Access Token

```http
POST /auth/token
Content-Type: application/json

{
    "username": "your_username",
    "password": "your_password"
}
```

**Response**:
```json
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "refresh_token": "def50200..."
}
```

### Using Access Token

Include the token in the Authorization header:
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Core Endpoints

### Tickets

#### Create Ticket
```http
POST /tickets
Content-Type: application/json

{
    "title": "Password reset request",
    "description": "User unable to access email account",
    "category": "password_reset",
    "priority": "medium",
    "client_id": "client_123",
    "requester_email": "<EMAIL>",
    "custom_fields": {
        "department": "Sales",
        "location": "New York"
    }
}
```

**Response**:
```json
{
    "ticket_id": "TKT-2024-001234",
    "status": "created",
    "assigned_agents": ["triage_agent"],
    "estimated_resolution_time": "2024-01-15T14:30:00Z",
    "sla_deadline": "2024-01-15T16:00:00Z",
    "created_at": "2024-01-15T12:00:00Z"
}
```

#### Get Ticket Details
```http
GET /tickets/{ticket_id}
```

**Response**:
```json
{
    "ticket_id": "TKT-2024-001234",
    "title": "Password reset request",
    "description": "User unable to access email account",
    "status": "in_progress",
    "priority": "medium",
    "category": "password_reset",
    "client_id": "client_123",
    "assigned_agents": ["triage_agent", "resolution_agent"],
    "resolution_steps": [
        {
            "step": 1,
            "action": "Verified user identity",
            "agent": "triage_agent",
            "timestamp": "2024-01-15T12:05:00Z"
        },
        {
            "step": 2,
            "action": "Initiated password reset",
            "agent": "resolution_agent",
            "timestamp": "2024-01-15T12:10:00Z"
        }
    ],
    "sla_status": "on_track",
    "created_at": "2024-01-15T12:00:00Z",
    "updated_at": "2024-01-15T12:10:00Z"
}
```

#### Update Ticket
```http
PUT /tickets/{ticket_id}
Content-Type: application/json

{
    "status": "resolved",
    "resolution": "Password reset completed successfully",
    "resolution_time": 15,
    "technician_notes": "User confirmed access restored"
}
```

#### List Tickets
```http
GET /tickets?status=open&priority=high&limit=50&offset=0
```

**Query Parameters**:
- `status`: open, in_progress, resolved, closed
- `priority`: low, medium, high, critical
- `category`: password_reset, software_install, network_issue, etc.
- `client_id`: Filter by client
- `assigned_agent`: Filter by assigned agent
- `created_after`: ISO 8601 timestamp
- `created_before`: ISO 8601 timestamp
- `limit`: Number of results (max 100)
- `offset`: Pagination offset

### Agents

#### List Agents
```http
GET /agents
```

**Response**:
```json
{
    "agents": [
        {
            "agent_id": "triage_agent_001",
            "agent_type": "triage",
            "status": "active",
            "current_load": 15,
            "max_capacity": 50,
            "performance_score": 0.94,
            "specializations": ["password_reset", "software_install"],
            "last_heartbeat": "2024-01-15T12:00:00Z"
        },
        {
            "agent_id": "resolution_agent_001",
            "agent_type": "resolution",
            "status": "active",
            "current_load": 8,
            "max_capacity": 20,
            "performance_score": 0.89,
            "specializations": ["automation", "api_integration"],
            "last_heartbeat": "2024-01-15T12:00:00Z"
        }
    ]
}
```

#### Get Agent Performance
```http
GET /agents/{agent_id}/performance?period=7d
```

**Response**:
```json
{
    "agent_id": "triage_agent_001",
    "period": "7d",
    "metrics": {
        "tickets_processed": 342,
        "average_processing_time": 1.8,
        "accuracy_rate": 0.94,
        "sla_compliance": 0.96,
        "automation_rate": 0.87
    },
    "performance_trend": "improving",
    "recommendations": [
        "Consider increasing capacity during peak hours",
        "Review classification rules for network issues"
    ]
}
```

#### Assign Task to Agent
```http
POST /agents/{agent_id}/tasks
Content-Type: application/json

{
    "task_type": "process_ticket",
    "ticket_id": "TKT-2024-001234",
    "priority": "high",
    "deadline": "2024-01-15T16:00:00Z",
    "context": {
        "client_tier": "enterprise",
        "escalation_path": "manager"
    }
}
```

### Knowledge Base

#### Search Knowledge
```http
GET /knowledge/search?q=password%20reset&category=authentication&limit=10
```

**Response**:
```json
{
    "results": [
        {
            "knowledge_id": "KB-001234",
            "title": "Standard Password Reset Procedure",
            "relevance_score": 0.95,
            "category": "authentication",
            "steps": [
                "Verify user identity using security questions",
                "Generate temporary password",
                "Send reset link via verified email",
                "Confirm password change completion"
            ],
            "success_rate": 0.98,
            "average_resolution_time": 5.2,
            "last_updated": "2024-01-10T10:00:00Z"
        }
    ],
    "total_results": 1,
    "search_time_ms": 45
}
```

#### Create Knowledge Entry
```http
POST /knowledge
Content-Type: application/json

{
    "title": "New Software Installation Procedure",
    "category": "software_management",
    "domain": "msp",
    "steps": [
        "Download software from approved repository",
        "Verify digital signature",
        "Install with elevated privileges",
        "Configure user permissions",
        "Test functionality"
    ],
    "prerequisites": ["admin_access", "antivirus_disabled"],
    "estimated_time": 15,
    "success_rate": 0.92,
    "source_tickets": ["TKT-2024-001100", "TKT-2024-001150"]
}
```

#### Get Knowledge Analytics
```http
GET /knowledge/analytics?period=30d
```

**Response**:
```json
{
    "period": "30d",
    "total_entries": 1247,
    "new_entries": 89,
    "updated_entries": 156,
    "most_accessed": [
        {
            "knowledge_id": "KB-001234",
            "title": "Password Reset Procedure",
            "access_count": 342,
            "success_rate": 0.98
        }
    ],
    "knowledge_gaps": [
        {
            "category": "network_troubleshooting",
            "gap_score": 0.73,
            "recommended_action": "Create knowledge entries for VPN issues"
        }
    ]
}
```

### Analytics

#### Get Dashboard Metrics
```http
GET /analytics/dashboard?period=7d
```

**Response**:
```json
{
    "period": "7d",
    "summary": {
        "total_tickets": 2847,
        "resolved_tickets": 2654,
        "automation_rate": 0.34,
        "average_resolution_time": 3.2,
        "sla_compliance": 0.94,
        "cost_savings": 145670.50
    },
    "trends": {
        "ticket_volume": [
            {"date": "2024-01-08", "count": 412},
            {"date": "2024-01-09", "count": 389},
            {"date": "2024-01-10", "count": 445}
        ],
        "resolution_time": [
            {"date": "2024-01-08", "avg_hours": 3.4},
            {"date": "2024-01-09", "avg_hours": 3.1},
            {"date": "2024-01-10", "avg_hours": 2.9}
        ]
    },
    "top_categories": [
        {"category": "password_reset", "count": 687, "automation_rate": 0.95},
        {"category": "software_install", "count": 423, "automation_rate": 0.78},
        {"category": "network_issue", "count": 312, "automation_rate": 0.45}
    ]
}
```

#### Get ROI Analysis
```http
GET /analytics/roi?period=12m&client_id=client_123
```

**Response**:
```json
{
    "client_id": "client_123",
    "period": "12m",
    "roi_metrics": {
        "total_cost_savings": 2847650.00,
        "implementation_cost": 150000.00,
        "net_benefit": 2697650.00,
        "roi_percentage": 1798.43,
        "payback_period_months": 0.63
    },
    "savings_breakdown": {
        "labor_cost_reduction": 1956780.00,
        "sla_penalty_reduction": 234560.00,
        "efficiency_gains": 656310.00
    },
    "automation_impact": {
        "tickets_automated": 8934,
        "hours_saved": 23456.7,
        "technician_productivity_gain": 0.47
    }
}
```

### SLA Management

#### Get SLA Status
```http
GET /sla/status?client_id=client_123
```

**Response**:
```json
{
    "client_id": "client_123",
    "overall_compliance": 0.94,
    "sla_tiers": [
        {
            "tier": "critical",
            "target_hours": 1,
            "compliance_rate": 0.98,
            "average_resolution": 0.8,
            "tickets_count": 23
        },
        {
            "tier": "high",
            "target_hours": 4,
            "compliance_rate": 0.95,
            "average_resolution": 3.2,
            "tickets_count": 156
        }
    ],
    "at_risk_tickets": [
        {
            "ticket_id": "TKT-2024-001245",
            "time_remaining_hours": 0.5,
            "breach_probability": 0.73,
            "recommended_action": "escalate_immediately"
        }
    ]
}
```

#### Predict SLA Breach
```http
POST /sla/predict
Content-Type: application/json

{
    "ticket_id": "TKT-2024-001234",
    "current_status": "in_progress",
    "assigned_agents": ["resolution_agent_001"],
    "complexity_factors": {
        "client_tier": "enterprise",
        "category": "network_issue",
        "priority": "high"
    }
}
```

**Response**:
```json
{
    "ticket_id": "TKT-2024-001234",
    "breach_probability": 0.23,
    "estimated_completion": "2024-01-15T15:30:00Z",
    "sla_deadline": "2024-01-15T16:00:00Z",
    "confidence_score": 0.87,
    "recommendations": [
        {
            "action": "assign_additional_agent",
            "impact": "reduce_time_by_30_minutes",
            "confidence": 0.82
        },
        {
            "action": "escalate_to_senior_tech",
            "impact": "reduce_time_by_45_minutes",
            "confidence": 0.91
        }
    ]
}
```

## Webhooks

### Webhook Configuration

```http
POST /webhooks
Content-Type: application/json

{
    "url": "https://your-system.com/webhooks/ticketsage",
    "events": ["ticket.created", "ticket.resolved", "sla.breach_warning"],
    "secret": "your_webhook_secret",
    "active": true
}
```

### Webhook Events

#### Ticket Created
```json
{
    "event": "ticket.created",
    "timestamp": "2024-01-15T12:00:00Z",
    "data": {
        "ticket_id": "TKT-2024-001234",
        "title": "Password reset request",
        "priority": "medium",
        "client_id": "client_123"
    }
}
```

#### SLA Breach Warning
```json
{
    "event": "sla.breach_warning",
    "timestamp": "2024-01-15T15:30:00Z",
    "data": {
        "ticket_id": "TKT-2024-001234",
        "time_remaining_minutes": 30,
        "breach_probability": 0.85,
        "recommended_actions": ["escalate_immediately"]
    }
}
```

## Error Handling

### Error Response Format
```json
{
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid ticket priority value",
        "details": {
            "field": "priority",
            "allowed_values": ["low", "medium", "high", "critical"]
        },
        "request_id": "req_123456789"
    }
}
```

### Common Error Codes
- `AUTHENTICATION_FAILED` (401): Invalid or expired token
- `AUTHORIZATION_DENIED` (403): Insufficient permissions
- `RESOURCE_NOT_FOUND` (404): Requested resource doesn't exist
- `VALIDATION_ERROR` (400): Invalid request data
- `RATE_LIMIT_EXCEEDED` (429): Too many requests
- `INTERNAL_ERROR` (500): Server error

## Rate Limiting

**Limits**:
- 1000 requests per minute per API key
- 10,000 requests per hour per API key
- 100,000 requests per day per API key

**Headers**:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

## SDKs and Libraries

### Python SDK
```python
from ticketsage import TicketSageClient

client = TicketSageClient(api_key="your_api_key")

# Create ticket
ticket = client.tickets.create(
    title="Password reset request",
    description="User unable to access email",
    category="password_reset",
    priority="medium"
)

# Search knowledge
results = client.knowledge.search("password reset")
```

### JavaScript SDK
```javascript
import { TicketSageClient } from '@ticketsage/sdk';

const client = new TicketSageClient({ apiKey: 'your_api_key' });

// Create ticket
const ticket = await client.tickets.create({
    title: 'Password reset request',
    description: 'User unable to access email',
    category: 'password_reset',
    priority: 'medium'
});

// Get analytics
const analytics = await client.analytics.getDashboard({ period: '7d' });
```

This API documentation provides comprehensive coverage of all TicketSage AI endpoints, enabling developers to integrate with the platform effectively.

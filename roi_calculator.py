"""
TicketSage AI ROI Calculator
Interactive calculator for demonstrating cost savings across industry verticals
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import numpy as np
from datetime import datetime, timedelta
from mock_data_generator import IndustryMockDataGenerator, IndustryVertical, CostMetrics

class ROICalculator:
    def __init__(self):
        self.industry_configs = {
            "Managed Service Provider (MSP)": {
                "vertical": IndustryVertical.MSP,
                "default_params": {
                    "monthly_tickets": 15000,
                    "avg_resolution_hours": 4.2,
                    "hourly_rate": 85,
                    "sla_compliance": 78,
                    "automation_potential": 32
                },
                "cost_factors": {
                    "technician_time": "Primary cost driver",
                    "sla_penalties": "Secondary cost",
                    "context_switching": "Hidden cost"
                }
            },
            "Healthcare System": {
                "vertical": IndustryVertical.HEALTHCARE,
                "default_params": {
                    "monthly_tickets": 8500,
                    "avg_resolution_hours": 2.8,
                    "hourly_rate": 45,
                    "sla_compliance": 72,
                    "automation_potential": 41
                },
                "cost_factors": {
                    "administrative_time": "Primary cost driver",
                    "compliance_overhead": "Major cost",
                    "patient_satisfaction": "Revenue impact"
                }
            },
            "Manufacturing Plant": {
                "vertical": IndustryVertical.MANUFACTURING,
                "default_params": {
                    "monthly_tickets": 2200,
                    "avg_resolution_hours": 3.2,
                    "hourly_rate": 95,
                    "downtime_cost_per_hour": 50000,
                    "automation_potential": 29
                },
                "cost_factors": {
                    "equipment_downtime": "Massive cost driver",
                    "maintenance_labor": "Secondary cost",
                    "quality_impact": "Hidden cost"
                }
            },
            "Financial Institution": {
                "vertical": IndustryVertical.FINANCIAL,
                "default_params": {
                    "monthly_tickets": 3800,
                    "avg_resolution_hours": 3.5,
                    "hourly_rate": 125,
                    "sla_compliance": 75,
                    "automation_potential": 35
                },
                "cost_factors": {
                    "compliance_time": "Primary cost driver",
                    "risk_assessment": "Major cost",
                    "regulatory_penalties": "High-risk cost"
                }
            },
            "Educational Institution": {
                "vertical": IndustryVertical.EDUCATION,
                "default_params": {
                    "monthly_tickets": 6200,
                    "avg_resolution_hours": 2.1,
                    "hourly_rate": 38,
                    "sla_compliance": 70,
                    "automation_potential": 38
                },
                "cost_factors": {
                    "administrative_time": "Primary cost driver",
                    "student_satisfaction": "Retention impact",
                    "efficiency_gains": "Budget optimization"
                }
            }
        }

def main():
    st.set_page_config(
        page_title="TicketSage AI ROI Calculator",
        page_icon="🎯",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    st.title("🎯 TicketSage AI ROI Calculator")
    st.markdown("**Demonstrate measurable cost savings across industry verticals**")
    
    calculator = ROICalculator()
    
    # Sidebar for industry selection and parameters
    with st.sidebar:
        st.header("Configuration")
        
        # Industry selection
        selected_industry = st.selectbox(
            "Select Industry Vertical",
            list(calculator.industry_configs.keys())
        )
        
        config = calculator.industry_configs[selected_industry]
        defaults = config["default_params"]
        
        st.subheader("Organization Parameters")
        
        # Organization size
        org_size = st.selectbox(
            "Organization Size",
            ["Small", "Medium", "Large"],
            index=1
        )
        
        # Adjustable parameters
        monthly_tickets = st.number_input(
            "Monthly Ticket Volume",
            min_value=100,
            max_value=100000,
            value=defaults["monthly_tickets"],
            step=500
        )
        
        avg_resolution_hours = st.slider(
            "Average Resolution Time (hours)",
            min_value=0.5,
            max_value=10.0,
            value=defaults["avg_resolution_hours"],
            step=0.1
        )
        
        hourly_rate = st.number_input(
            "Staff Hourly Rate ($)",
            min_value=20,
            max_value=200,
            value=defaults["hourly_rate"],
            step=5
        )
        
        sla_compliance = st.slider(
            "Current SLA Compliance (%)",
            min_value=50,
            max_value=95,
            value=defaults["sla_compliance"],
            step=1
        )
        
        # Industry-specific parameters
        if selected_industry == "Manufacturing Plant":
            downtime_cost = st.number_input(
                "Downtime Cost per Hour ($)",
                min_value=1000,
                max_value=200000,
                value=defaults.get("downtime_cost_per_hour", 50000),
                step=5000
            )
        
        automation_potential = st.slider(
            "TicketSage Automation Potential (%)",
            min_value=20,
            max_value=60,
            value=defaults["automation_potential"],
            step=1
        )
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Calculate current state metrics
        current_cost_per_ticket = avg_resolution_hours * hourly_rate
        monthly_cost = monthly_tickets * current_cost_per_ticket
        annual_cost = monthly_cost * 12
        
        # Add industry-specific costs
        if selected_industry == "Manufacturing Plant":
            # Add downtime costs for equipment-related tickets (assume 35% of tickets)
            equipment_tickets = monthly_tickets * 0.35
            monthly_downtime_cost = equipment_tickets * avg_resolution_hours * downtime_cost
            monthly_cost += monthly_downtime_cost
            annual_cost = monthly_cost * 12
        
        # Calculate TicketSage improvements
        automation_rate = automation_potential / 100
        efficiency_improvement = 0.57  # Average 57% efficiency gain
        sla_improvement = 16  # Average 16% SLA improvement
        
        # Calculate savings
        automated_tickets = monthly_tickets * automation_rate
        automated_savings = automated_tickets * current_cost_per_ticket
        
        remaining_tickets = monthly_tickets - automated_tickets
        efficiency_savings = remaining_tickets * current_cost_per_ticket * efficiency_improvement
        
        total_monthly_savings = automated_savings + efficiency_savings
        
        # Add industry-specific savings
        if selected_industry == "Manufacturing Plant":
            downtime_reduction_savings = monthly_downtime_cost * 0.62  # 62% downtime reduction
            total_monthly_savings += downtime_reduction_savings
        
        annual_savings = total_monthly_savings * 12
        
        # Implementation cost (estimated)
        implementation_cost = 150000  # Base implementation cost
        annual_roi = ((annual_savings - implementation_cost) / implementation_cost) * 100
        
        # Display key metrics
        st.subheader("📊 Cost Savings Analysis")
        
        metric_cols = st.columns(4)
        with metric_cols[0]:
            st.metric(
                "Monthly Savings",
                f"${total_monthly_savings:,.0f}",
                f"{(total_monthly_savings/monthly_cost)*100:.1f}% reduction"
            )
        
        with metric_cols[1]:
            st.metric(
                "Annual Savings",
                f"${annual_savings:,.0f}",
                f"${annual_savings/12:,.0f}/month"
            )
        
        with metric_cols[2]:
            st.metric(
                "ROI (Year 1)",
                f"{annual_roi:.0f}%",
                f"Payback: {(implementation_cost/total_monthly_savings):.1f} months"
            )
        
        with metric_cols[3]:
            st.metric(
                "Automation Rate",
                f"{automation_rate*100:.0f}%",
                f"{automated_tickets:,.0f} tickets/month"
            )
        
        # Cost breakdown chart
        st.subheader("💰 Cost Breakdown Comparison")
        
        # Create before/after comparison
        categories = ['Labor Cost', 'SLA Penalties', 'Inefficiencies']
        if selected_industry == "Manufacturing Plant":
            categories.append('Downtime Cost')
        
        current_costs = [
            monthly_tickets * current_cost_per_ticket,
            monthly_cost * 0.05,  # Estimated SLA penalty cost
            monthly_cost * 0.15   # Estimated inefficiency cost
        ]
        
        future_costs = [
            (monthly_tickets - automated_tickets) * current_cost_per_ticket * (1 - efficiency_improvement),
            monthly_cost * 0.01,  # Reduced SLA penalties
            monthly_cost * 0.05   # Reduced inefficiencies
        ]
        
        if selected_industry == "Manufacturing Plant":
            current_costs.append(monthly_downtime_cost)
            future_costs.append(monthly_downtime_cost * 0.38)  # 62% reduction
        
        fig = go.Figure(data=[
            go.Bar(name='Current State', x=categories, y=current_costs, marker_color='#ff6b6b'),
            go.Bar(name='With TicketSage AI', x=categories, y=future_costs, marker_color='#4ecdc4')
        ])
        
        fig.update_layout(
            title="Monthly Cost Comparison",
            xaxis_title="Cost Categories",
            yaxis_title="Monthly Cost ($)",
            barmode='group',
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # ROI Timeline
        st.subheader("📈 ROI Timeline (3 Years)")
        
        months = list(range(1, 37))
        cumulative_savings = [total_monthly_savings * month for month in months]
        cumulative_investment = [implementation_cost] * 36
        net_benefit = [savings - implementation_cost for savings in cumulative_savings]
        
        fig_timeline = go.Figure()
        fig_timeline.add_trace(go.Scatter(
            x=months, y=cumulative_savings,
            mode='lines', name='Cumulative Savings',
            line=dict(color='#4ecdc4', width=3)
        ))
        fig_timeline.add_trace(go.Scatter(
            x=months, y=cumulative_investment,
            mode='lines', name='Implementation Cost',
            line=dict(color='#ff6b6b', width=2, dash='dash')
        ))
        fig_timeline.add_trace(go.Scatter(
            x=months, y=net_benefit,
            mode='lines', name='Net Benefit',
            line=dict(color='#45b7d1', width=3),
            fill='tonexty'
        ))
        
        fig_timeline.update_layout(
            title="ROI Timeline - Cumulative Financial Impact",
            xaxis_title="Months",
            yaxis_title="Cumulative Value ($)",
            height=400
        )
        
        st.plotly_chart(fig_timeline, use_container_width=True)
    
    with col2:
        st.subheader("🎯 Key Benefits")
        
        # Industry-specific benefits
        benefits = {
            "Managed Service Provider (MSP)": [
                "32% of tickets resolved automatically",
                "57% faster resolution times",
                "16% improvement in SLA compliance",
                "Reduced technician burnout",
                "Improved client satisfaction"
            ],
            "Healthcare System": [
                "41% automation of admin tasks",
                "HIPAA compliance automation",
                "Improved patient satisfaction",
                "Reduced documentation time",
                "Better resource allocation"
            ],
            "Manufacturing Plant": [
                "62% reduction in unplanned downtime",
                "Predictive maintenance scheduling",
                "Quality improvement tracking",
                "Supply chain optimization",
                "Safety incident reduction"
            ],
            "Financial Institution": [
                "35% automation of compliance tasks",
                "Real-time risk assessment",
                "Automated regulatory reporting",
                "Reduced audit preparation time",
                "Improved fraud detection"
            ],
            "Educational Institution": [
                "38% automation of admin processes",
                "Improved student satisfaction",
                "Faster transcript processing",
                "Better resource utilization",
                "Enhanced student retention"
            ]
        }
        
        for benefit in benefits[selected_industry]:
            st.success(f"✅ {benefit}")
        
        st.subheader("📋 Implementation Details")
        
        implementation_phases = [
            "**Phase 1 (Month 1-2)**: Core platform setup and basic automation",
            "**Phase 2 (Month 3-4)**: Advanced AI models and integrations",
            "**Phase 3 (Month 5-6)**: Domain-specific customization and optimization"
        ]
        
        for phase in implementation_phases:
            st.markdown(phase)
        
        st.subheader("📊 Risk Factors")
        
        risk_factors = [
            "**Low Risk**: Proven technology stack",
            "**Medium Risk**: Integration complexity",
            "**Mitigation**: Phased rollout approach"
        ]
        
        for risk in risk_factors:
            st.markdown(risk)
        
        # Download report button
        if st.button("📄 Generate ROI Report", type="primary"):
            # Create downloadable report
            report_data = {
                "Industry": selected_industry,
                "Monthly Tickets": monthly_tickets,
                "Current Monthly Cost": f"${monthly_cost:,.0f}",
                "Monthly Savings": f"${total_monthly_savings:,.0f}",
                "Annual Savings": f"${annual_savings:,.0f}",
                "ROI Year 1": f"{annual_roi:.0f}%",
                "Automation Rate": f"{automation_rate*100:.0f}%",
                "Payback Period": f"{(implementation_cost/total_monthly_savings):.1f} months"
            }
            
            st.success("✅ ROI Report Generated!")
            st.json(report_data)

if __name__ == "__main__":
    main()

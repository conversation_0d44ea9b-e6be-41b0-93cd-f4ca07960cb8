# TicketSage AI - Data Architecture & Database Design

## Data Architecture Overview

The TicketSage AI platform employs a polyglot persistence approach, using different database technologies optimized for specific data patterns and access requirements. This ensures optimal performance, scalability, and cost-effectiveness.

## Database Strategy

### **Operational Data Store: Amazon Aurora PostgreSQL**
**Purpose**: ACID-compliant transactional data for core business operations

**Configuration**:
```yaml
Service: Amazon Aurora PostgreSQL Serverless v2
Version: PostgreSQL 14.9
Scaling: 0.5-16 ACUs (Auto-scaling)
Availability: Multi-AZ with read replicas
Backup: 7-day retention with point-in-time recovery
Encryption: AES-256 with <PERSON><PERSON> KMS
```

**Schema Design**:
```sql
-- Core Tables
CREATE SCHEMA ticketsage;

-- Clients and Organizations
CREATE TABLE ticketsage.clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(100) NOT NULL, -- msp, healthcare, manufacturing, etc.
    tier VARCHAR(50) NOT NULL, -- enterprise, premium, standard
    settings JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tickets - Core entity
CREATE TABLE ticketsage.tickets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    external_id VARCHAR(100), -- ID from source system
    client_id UUID NOT NULL REFERENCES ticketsage.clients(id),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    subcategory VARCHAR(100),
    priority VARCHAR(20) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(50) NOT NULL CHECK (status IN ('open', 'in_progress', 'resolved', 'closed', 'escalated')),
    requester_email VARCHAR(255),
    assigned_to VARCHAR(255),
    sla_deadline TIMESTAMP WITH TIME ZONE,
    resolution TEXT,
    resolution_time_minutes INTEGER,
    cost_estimate DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    custom_fields JSONB,
    source_system VARCHAR(50), -- autotask, itglue, manual, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Ticket Comments and Updates
CREATE TABLE ticketsage.ticket_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id UUID NOT NULL REFERENCES ticketsage.tickets(id) ON DELETE CASCADE,
    author VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE,
    comment_type VARCHAR(50) DEFAULT 'comment', -- comment, status_change, assignment
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ticket Attachments
CREATE TABLE ticketsage.ticket_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id UUID NOT NULL REFERENCES ticketsage.tickets(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    s3_bucket VARCHAR(100),
    s3_key VARCHAR(500),
    uploaded_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Agents Registry
CREATE TABLE ticketsage.agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- triage, resolution, knowledge, sla_guardian, etc.
    version VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'inactive', 'maintenance', 'error')),
    capabilities TEXT[], -- array of capabilities
    specializations TEXT[], -- array of specializations
    current_load INTEGER DEFAULT 0,
    max_capacity INTEGER DEFAULT 10,
    performance_score DECIMAL(3,2) DEFAULT 0.0,
    configuration JSONB,
    last_heartbeat TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflows and Orchestration
CREATE TABLE ticketsage.workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id UUID NOT NULL REFERENCES ticketsage.tickets(id),
    status VARCHAR(50) NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    workflow_type VARCHAR(100) NOT NULL,
    assigned_agents UUID[],
    steps JSONB NOT NULL, -- array of workflow steps
    conflicts JSONB, -- array of conflicts and resolutions
    execution_time_seconds INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Agent Tasks
CREATE TABLE ticketsage.agent_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES ticketsage.workflows(id),
    agent_id UUID NOT NULL REFERENCES ticketsage.agents(id),
    task_type VARCHAR(100) NOT NULL,
    priority INTEGER DEFAULT 5,
    input_data JSONB,
    output_data JSONB,
    status VARCHAR(50) NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    execution_time_seconds INTEGER,
    error_message TEXT,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Knowledge Base Metadata
CREATE TABLE ticketsage.knowledge_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    domain VARCHAR(50) NOT NULL,
    steps TEXT[], -- array of resolution steps
    prerequisites TEXT[], -- array of prerequisites
    success_rate DECIMAL(3,2) DEFAULT 0.0,
    confidence_score DECIMAL(3,2) DEFAULT 0.0,
    source_tickets UUID[], -- array of source ticket IDs
    tags TEXT[],
    vector_id VARCHAR(100), -- reference to vector in Pinecone
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SLA Configurations
CREATE TABLE ticketsage.sla_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES ticketsage.clients(id),
    name VARCHAR(255) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    category VARCHAR(100),
    response_time_minutes INTEGER,
    resolution_time_minutes INTEGER,
    escalation_rules JSONB,
    business_hours JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit Trail
CREATE TABLE ticketsage.audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type VARCHAR(50) NOT NULL, -- ticket, agent, workflow, etc.
    entity_id UUID NOT NULL,
    action VARCHAR(100) NOT NULL,
    actor VARCHAR(255) NOT NULL, -- user or agent ID
    old_values JSONB,
    new_values JSONB,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for Performance
CREATE INDEX idx_tickets_client_id ON ticketsage.tickets(client_id);
CREATE INDEX idx_tickets_status ON ticketsage.tickets(status);
CREATE INDEX idx_tickets_category ON ticketsage.tickets(category);
CREATE INDEX idx_tickets_created_at ON ticketsage.tickets(created_at);
CREATE INDEX idx_tickets_sla_deadline ON ticketsage.tickets(sla_deadline) WHERE sla_deadline IS NOT NULL;
CREATE INDEX idx_ticket_comments_ticket_id ON ticketsage.ticket_comments(ticket_id);
CREATE INDEX idx_workflows_ticket_id ON ticketsage.workflows(ticket_id);
CREATE INDEX idx_agent_tasks_workflow_id ON ticketsage.agent_tasks(workflow_id);
CREATE INDEX idx_agent_tasks_agent_id ON ticketsage.agent_tasks(agent_id);
CREATE INDEX idx_knowledge_entries_category ON ticketsage.knowledge_entries(category);
CREATE INDEX idx_knowledge_entries_domain ON ticketsage.knowledge_entries(domain);
CREATE INDEX idx_audit_logs_entity ON ticketsage.audit_logs(entity_type, entity_id);
```

### **Analytical Data Store: Amazon Redshift**
**Purpose**: Data warehouse for analytics, reporting, and business intelligence

**Configuration**:
```yaml
Service: Amazon Redshift Serverless
Base Capacity: 32 RPUs
Auto-scaling: Enabled
Data Sharing: Enabled for cross-account analytics
Encryption: AES-256 with AWS KMS
Backup: Automated snapshots with 7-day retention
```

**Schema Design**:
```sql
-- Dimensional Model for Analytics
CREATE SCHEMA analytics;

-- Date Dimension
CREATE TABLE analytics.dim_date (
    date_key INTEGER PRIMARY KEY,
    date_value DATE NOT NULL,
    year INTEGER NOT NULL,
    quarter INTEGER NOT NULL,
    month INTEGER NOT NULL,
    week INTEGER NOT NULL,
    day_of_week INTEGER NOT NULL,
    day_name VARCHAR(10) NOT NULL,
    month_name VARCHAR(10) NOT NULL,
    is_weekend BOOLEAN NOT NULL,
    is_holiday BOOLEAN DEFAULT FALSE
);

-- Client Dimension
CREATE TABLE analytics.dim_client (
    client_key INTEGER IDENTITY(1,1) PRIMARY KEY,
    client_id VARCHAR(36) NOT NULL,
    client_name VARCHAR(255) NOT NULL,
    domain VARCHAR(50) NOT NULL,
    tier VARCHAR(50) NOT NULL,
    effective_date DATE NOT NULL,
    expiry_date DATE DEFAULT '9999-12-31'
);

-- Agent Dimension
CREATE TABLE analytics.dim_agent (
    agent_key INTEGER IDENTITY(1,1) PRIMARY KEY,
    agent_id VARCHAR(36) NOT NULL,
    agent_name VARCHAR(255) NOT NULL,
    agent_type VARCHAR(50) NOT NULL,
    capabilities VARCHAR(1000),
    effective_date DATE NOT NULL,
    expiry_date DATE DEFAULT '9999-12-31'
);

-- Category Dimension
CREATE TABLE analytics.dim_category (
    category_key INTEGER IDENTITY(1,1) PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    subcategory VARCHAR(100),
    domain VARCHAR(50) NOT NULL,
    automation_potential DECIMAL(3,2)
);

-- Ticket Facts
CREATE TABLE analytics.fact_tickets (
    ticket_key INTEGER IDENTITY(1,1) PRIMARY KEY,
    ticket_id VARCHAR(36) NOT NULL,
    client_key INTEGER NOT NULL REFERENCES analytics.dim_client(client_key),
    category_key INTEGER NOT NULL REFERENCES analytics.dim_category(category_key),
    created_date_key INTEGER NOT NULL REFERENCES analytics.dim_date(date_key),
    resolved_date_key INTEGER REFERENCES analytics.dim_date(date_key),
    priority VARCHAR(20) NOT NULL,
    status VARCHAR(50) NOT NULL,
    resolution_time_minutes INTEGER,
    cost_estimate DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    sla_met BOOLEAN,
    automated BOOLEAN DEFAULT FALSE,
    escalated BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL,
    resolved_at TIMESTAMP
);

-- Agent Performance Facts
CREATE TABLE analytics.fact_agent_performance (
    performance_key INTEGER IDENTITY(1,1) PRIMARY KEY,
    agent_key INTEGER NOT NULL REFERENCES analytics.dim_agent(agent_key),
    date_key INTEGER NOT NULL REFERENCES analytics.dim_date(date_key),
    tasks_assigned INTEGER DEFAULT 0,
    tasks_completed INTEGER DEFAULT 0,
    tasks_failed INTEGER DEFAULT 0,
    avg_execution_time_seconds DECIMAL(10,2),
    performance_score DECIMAL(3,2),
    load_percentage DECIMAL(5,2)
);

-- Daily Metrics Summary
CREATE TABLE analytics.fact_daily_metrics (
    metric_key INTEGER IDENTITY(1,1) PRIMARY KEY,
    client_key INTEGER NOT NULL REFERENCES analytics.dim_client(client_key),
    date_key INTEGER NOT NULL REFERENCES analytics.dim_date(date_key),
    total_tickets INTEGER DEFAULT 0,
    resolved_tickets INTEGER DEFAULT 0,
    automated_tickets INTEGER DEFAULT 0,
    escalated_tickets INTEGER DEFAULT 0,
    avg_resolution_time_minutes DECIMAL(10,2),
    sla_compliance_rate DECIMAL(3,2),
    total_cost DECIMAL(12,2),
    cost_savings DECIMAL(12,2)
);
```

### **Vector Database: Pinecone**
**Purpose**: Semantic search and similarity matching for knowledge retrieval

**Configuration**:
```yaml
Environment: us-west1-gcp
Pod Type: p1.x1 (1 pod, 1 replica)
Dimensions: 768 (sentence-transformers/all-MiniLM-L6-v2)
Metric: cosine similarity
```

**Index Structure**:
```python
# Knowledge Base Index
knowledge_index = {
    "name": "knowledge-base",
    "dimension": 768,
    "metric": "cosine",
    "metadata_config": {
        "indexed": ["category", "domain", "success_rate", "created_date"]
    }
}

# Ticket Embeddings Index
ticket_index = {
    "name": "ticket-embeddings", 
    "dimension": 768,
    "metric": "cosine",
    "metadata_config": {
        "indexed": ["category", "priority", "client_id", "created_date"]
    }
}

# Vector Metadata Schema
vector_metadata = {
    "id": "knowledge_entry_uuid",
    "category": "password_reset",
    "domain": "msp",
    "success_rate": 0.95,
    "confidence_score": 0.87,
    "created_date": "2024-01-15",
    "tags": ["authentication", "active_directory"],
    "source_tickets": ["ticket_uuid_1", "ticket_uuid_2"]
}
```

### **Cache Layer: Amazon ElastiCache for Redis**
**Purpose**: High-performance caching for frequently accessed data

**Configuration**:
```yaml
Node Type: cache.r6g.large
Cluster Mode: Enabled (3 shards, 1 replica each)
Multi-AZ: Enabled
Encryption: In transit and at rest
```

**Cache Patterns**:
```python
# Cache Key Patterns
cache_patterns = {
    # Session Management
    "session:{user_id}": {
        "ttl": 86400,  # 24 hours
        "data": "user session data"
    },
    
    # API Response Caching
    "api:tickets:{client_id}:page:{page}": {
        "ttl": 300,  # 5 minutes
        "data": "paginated ticket list"
    },
    
    # Agent Status
    "agent:{agent_id}:status": {
        "ttl": 60,  # 1 minute
        "data": "agent health and load"
    },
    
    # Knowledge Recommendations
    "knowledge:recommendations:{ticket_id}": {
        "ttl": 3600,  # 1 hour
        "data": "relevant knowledge entries"
    },
    
    # Rate Limiting
    "rate_limit:{api_key}:{endpoint}": {
        "ttl": 60,  # 1 minute
        "data": "request count"
    },
    
    # SLA Calculations
    "sla:prediction:{ticket_id}": {
        "ttl": 1800,  # 30 minutes
        "data": "sla breach prediction"
    }
}
```

### **Search Engine: Amazon OpenSearch**
**Purpose**: Full-text search and log analytics

**Configuration**:
```yaml
Version: OpenSearch 2.3
Instance Type: t3.small.search (3 nodes)
Storage: 20GB EBS per node
Multi-AZ: Enabled
```

**Index Mappings**:
```json
{
  "tickets": {
    "mappings": {
      "properties": {
        "id": {"type": "keyword"},
        "title": {"type": "text", "analyzer": "standard"},
        "description": {"type": "text", "analyzer": "standard"},
        "category": {"type": "keyword"},
        "priority": {"type": "keyword"},
        "status": {"type": "keyword"},
        "client_id": {"type": "keyword"},
        "created_at": {"type": "date"},
        "resolved_at": {"type": "date"},
        "tags": {"type": "keyword"},
        "full_text": {"type": "text", "analyzer": "standard"}
      }
    }
  },
  "knowledge": {
    "mappings": {
      "properties": {
        "id": {"type": "keyword"},
        "title": {"type": "text", "analyzer": "standard"},
        "content": {"type": "text", "analyzer": "standard"},
        "category": {"type": "keyword"},
        "domain": {"type": "keyword"},
        "steps": {"type": "text"},
        "tags": {"type": "keyword"},
        "success_rate": {"type": "float"},
        "created_at": {"type": "date"}
      }
    }
  },
  "audit_logs": {
    "mappings": {
      "properties": {
        "id": {"type": "keyword"},
        "entity_type": {"type": "keyword"},
        "entity_id": {"type": "keyword"},
        "action": {"type": "keyword"},
        "actor": {"type": "keyword"},
        "timestamp": {"type": "date"},
        "metadata": {"type": "object", "enabled": false}
      }
    }
  }
}
```

## Data Pipeline Architecture

### **ETL Pipeline: AWS Glue**
**Purpose**: Extract, transform, and load data between systems

**Jobs**:
```python
# Daily ETL Job: Operational to Analytical
etl_jobs = {
    "daily_ticket_etl": {
        "source": "Aurora PostgreSQL",
        "target": "Redshift",
        "schedule": "0 2 * * *",  # Daily at 2 AM
        "transformations": [
            "dimension_lookup",
            "fact_aggregation",
            "data_quality_checks"
        ]
    },
    
    "knowledge_vector_sync": {
        "source": "PostgreSQL + OpenSearch",
        "target": "Pinecone",
        "schedule": "0 */4 * * *",  # Every 4 hours
        "transformations": [
            "text_embedding_generation",
            "metadata_enrichment",
            "vector_upsert"
        ]
    },
    
    "real_time_metrics": {
        "source": "Kafka Streams",
        "target": "Redis + CloudWatch",
        "type": "streaming",
        "transformations": [
            "event_aggregation",
            "metric_calculation",
            "alert_generation"
        ]
    }
}
```

### **Data Quality Framework**
```python
# Data Quality Rules
quality_rules = {
    "tickets": [
        {"field": "id", "rule": "not_null"},
        {"field": "client_id", "rule": "not_null"},
        {"field": "title", "rule": "not_empty"},
        {"field": "priority", "rule": "in_list", "values": ["low", "medium", "high", "critical"]},
        {"field": "created_at", "rule": "valid_date"},
        {"field": "resolution_time_minutes", "rule": "positive_number"}
    ],
    
    "knowledge_entries": [
        {"field": "id", "rule": "not_null"},
        {"field": "title", "rule": "not_empty"},
        {"field": "content", "rule": "min_length", "value": 50},
        {"field": "success_rate", "rule": "between", "min": 0.0, "max": 1.0},
        {"field": "confidence_score", "rule": "between", "min": 0.0, "max": 1.0}
    ]
}
```

This data architecture provides a robust foundation for TicketSage AI with optimized storage for different data patterns, efficient querying capabilities, and scalable analytics infrastructure.

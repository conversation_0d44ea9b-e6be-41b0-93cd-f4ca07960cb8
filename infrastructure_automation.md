# TicketSage AI - Infrastructure Automation & Deployment

## Infrastructure as Code Strategy

The TicketSage AI platform uses a comprehensive Infrastructure as Code (IaC) approach with Terraform for infrastructure provisioning and Helm charts for application deployment, ensuring reproducible, version-controlled, and automated infrastructure management.

## Terraform Infrastructure Modules

### **Root Module Structure**
```hcl
# terraform/
├── environments/
│   ├── dev/
│   ├── staging/
│   └── production/
├── modules/
│   ├── vpc/
│   ├── eks/
│   ├── rds/
│   ├── msk/
│   ├── elasticache/
│   ├── opensearch/
│   └── monitoring/
└── shared/
    ├── variables.tf
    ├── outputs.tf
    └── versions.tf
```

### **VPC Module**
```hcl
# modules/vpc/main.tf
resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "${var.environment}-ticketsage-vpc"
    Environment = var.environment
    Project     = "ticketsage-ai"
  }
}

# Public subnets for load balancers
resource "aws_subnet" "public" {
  count = length(var.availability_zones)

  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.public_subnet_cidrs[count.index]
  availability_zone       = var.availability_zones[count.index]
  map_public_ip_on_launch = true

  tags = {
    Name                     = "${var.environment}-public-${var.availability_zones[count.index]}"
    "kubernetes.io/role/elb" = "1"
    Environment              = var.environment
  }
}

# Private subnets for applications
resource "aws_subnet" "private" {
  count = length(var.availability_zones)

  vpc_id            = aws_vpc.main.id
  cidr_block        = var.private_subnet_cidrs[count.index]
  availability_zone = var.availability_zones[count.index]

  tags = {
    Name                              = "${var.environment}-private-${var.availability_zones[count.index]}"
    "kubernetes.io/role/internal-elb" = "1"
    Environment                       = var.environment
  }
}

# Database subnets
resource "aws_subnet" "database" {
  count = length(var.availability_zones)

  vpc_id            = aws_vpc.main.id
  cidr_block        = var.database_subnet_cidrs[count.index]
  availability_zone = var.availability_zones[count.index]

  tags = {
    Name        = "${var.environment}-database-${var.availability_zones[count.index]}"
    Environment = var.environment
  }
}

# Internet Gateway
resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name        = "${var.environment}-ticketsage-igw"
    Environment = var.environment
  }
}

# NAT Gateways
resource "aws_eip" "nat" {
  count = length(var.availability_zones)

  domain = "vpc"
  depends_on = [aws_internet_gateway.main]

  tags = {
    Name        = "${var.environment}-nat-eip-${count.index + 1}"
    Environment = var.environment
  }
}

resource "aws_nat_gateway" "main" {
  count = length(var.availability_zones)

  allocation_id = aws_eip.nat[count.index].id
  subnet_id     = aws_subnet.public[count.index].id

  tags = {
    Name        = "${var.environment}-nat-gateway-${count.index + 1}"
    Environment = var.environment
  }
}
```

### **EKS Module**
```hcl
# modules/eks/main.tf
resource "aws_eks_cluster" "main" {
  name     = "${var.environment}-ticketsage-eks"
  role_arn = aws_iam_role.cluster.arn
  version  = var.kubernetes_version

  vpc_config {
    subnet_ids              = var.subnet_ids
    endpoint_private_access = true
    endpoint_public_access  = true
    public_access_cidrs     = var.public_access_cidrs
  }

  encryption_config {
    provider {
      key_arn = aws_kms_key.eks.arn
    }
    resources = ["secrets"]
  }

  enabled_cluster_log_types = ["api", "audit", "authenticator", "controllerManager", "scheduler"]

  depends_on = [
    aws_iam_role_policy_attachment.cluster_AmazonEKSClusterPolicy,
    aws_cloudwatch_log_group.eks_cluster,
  ]

  tags = {
    Environment = var.environment
    Project     = "ticketsage-ai"
  }
}

# Managed Node Groups
resource "aws_eks_node_group" "general" {
  cluster_name    = aws_eks_cluster.main.name
  node_group_name = "general-purpose"
  node_role_arn   = aws_iam_role.node_group.arn
  subnet_ids      = var.private_subnet_ids

  capacity_type  = "ON_DEMAND"
  instance_types = ["m5.large"]

  scaling_config {
    desired_size = var.node_group_desired_size
    max_size     = var.node_group_max_size
    min_size     = var.node_group_min_size
  }

  update_config {
    max_unavailable = 1
  }

  # Ensure that IAM Role permissions are created before and deleted after EKS Node Group handling.
  depends_on = [
    aws_iam_role_policy_attachment.node_group_AmazonEKSWorkerNodePolicy,
    aws_iam_role_policy_attachment.node_group_AmazonEKS_CNI_Policy,
    aws_iam_role_policy_attachment.node_group_AmazonEC2ContainerRegistryReadOnly,
  ]

  tags = {
    Environment = var.environment
    Project     = "ticketsage-ai"
  }
}

# Spot Instance Node Group for cost optimization
resource "aws_eks_node_group" "spot" {
  cluster_name    = aws_eks_cluster.main.name
  node_group_name = "spot-instances"
  node_role_arn   = aws_iam_role.node_group.arn
  subnet_ids      = var.private_subnet_ids

  capacity_type  = "SPOT"
  instance_types = ["m5.large", "m5.xlarge", "m4.large"]

  scaling_config {
    desired_size = var.spot_node_group_desired_size
    max_size     = var.spot_node_group_max_size
    min_size     = var.spot_node_group_min_size
  }

  depends_on = [
    aws_iam_role_policy_attachment.node_group_AmazonEKSWorkerNodePolicy,
    aws_iam_role_policy_attachment.node_group_AmazonEKS_CNI_Policy,
    aws_iam_role_policy_attachment.node_group_AmazonEC2ContainerRegistryReadOnly,
  ]

  tags = {
    Environment = var.environment
    Project     = "ticketsage-ai"
    NodeType    = "spot"
  }
}
```

### **RDS Aurora Module**
```hcl
# modules/rds/main.tf
resource "aws_rds_cluster" "aurora_postgresql" {
  cluster_identifier      = "${var.environment}-ticketsage-aurora"
  engine                  = "aurora-postgresql"
  engine_mode             = "provisioned"
  engine_version          = var.postgres_version
  database_name           = var.database_name
  master_username         = var.master_username
  manage_master_user_password = true
  
  serverlessv2_scaling_configuration {
    max_capacity = var.max_capacity
    min_capacity = var.min_capacity
  }

  db_subnet_group_name   = aws_db_subnet_group.aurora.name
  vpc_security_group_ids = [aws_security_group.aurora.id]

  backup_retention_period = var.backup_retention_period
  preferred_backup_window = "03:00-04:00"
  preferred_maintenance_window = "sun:04:00-sun:05:00"

  storage_encrypted = true
  kms_key_id       = aws_kms_key.aurora.arn

  enabled_cloudwatch_logs_exports = ["postgresql"]

  deletion_protection = var.deletion_protection

  tags = {
    Name        = "${var.environment}-ticketsage-aurora"
    Environment = var.environment
    Project     = "ticketsage-ai"
  }
}

resource "aws_rds_cluster_instance" "aurora_instances" {
  count              = var.instance_count
  identifier         = "${var.environment}-ticketsage-aurora-${count.index}"
  cluster_identifier = aws_rds_cluster.aurora_postgresql.id
  instance_class     = "db.serverless"
  engine             = aws_rds_cluster.aurora_postgresql.engine
  engine_version     = aws_rds_cluster.aurora_postgresql.engine_version

  performance_insights_enabled = true
  monitoring_interval          = 60
  monitoring_role_arn          = aws_iam_role.rds_enhanced_monitoring.arn

  tags = {
    Name        = "${var.environment}-ticketsage-aurora-${count.index}"
    Environment = var.environment
    Project     = "ticketsage-ai"
  }
}
```

### **MSK (Kafka) Module**
```hcl
# modules/msk/main.tf
resource "aws_msk_cluster" "kafka" {
  cluster_name           = "${var.environment}-ticketsage-kafka"
  kafka_version          = var.kafka_version
  number_of_broker_nodes = var.number_of_broker_nodes

  broker_node_group_info {
    instance_type   = var.broker_instance_type
    ebs_volume_size = var.ebs_volume_size
    client_subnets  = var.subnet_ids
    security_groups = [aws_security_group.msk.id]
  }

  encryption_info {
    encryption_at_rest_kms_key_id = aws_kms_key.msk.arn
    encryption_in_transit {
      client_broker = "TLS"
      in_cluster    = true
    }
  }

  configuration_info {
    arn      = aws_msk_configuration.kafka_config.arn
    revision = aws_msk_configuration.kafka_config.latest_revision
  }

  logging_info {
    broker_logs {
      cloudwatch_logs {
        enabled   = true
        log_group = aws_cloudwatch_log_group.msk.name
      }
    }
  }

  tags = {
    Name        = "${var.environment}-ticketsage-kafka"
    Environment = var.environment
    Project     = "ticketsage-ai"
  }
}

resource "aws_msk_configuration" "kafka_config" {
  kafka_versions = [var.kafka_version]
  name           = "${var.environment}-ticketsage-kafka-config"

  server_properties = <<PROPERTIES
auto.create.topics.enable=false
default.replication.factor=3
min.insync.replicas=2
num.partitions=3
log.retention.hours=168
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000
PROPERTIES
}
```

## Helm Charts for Application Deployment

### **Chart Structure**
```yaml
# helm/ticketsage-ai/
├── Chart.yaml
├── values.yaml
├── values-dev.yaml
├── values-staging.yaml
├── values-production.yaml
└── templates/
    ├── configmap.yaml
    ├── secret.yaml
    ├── deployment.yaml
    ├── service.yaml
    ├── ingress.yaml
    ├── hpa.yaml
    └── servicemonitor.yaml
```

### **Main Application Deployment**
```yaml
# helm/ticketsage-ai/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ticketsage-ai.fullname" . }}-orchestrator
  labels:
    {{- include "ticketsage-ai.labels" . | nindent 4 }}
    component: orchestrator
spec:
  replicas: {{ .Values.orchestrator.replicaCount }}
  selector:
    matchLabels:
      {{- include "ticketsage-ai.selectorLabels" . | nindent 6 }}
      component: orchestrator
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
      labels:
        {{- include "ticketsage-ai.selectorLabels" . | nindent 8 }}
        component: orchestrator
    spec:
      serviceAccountName: {{ include "ticketsage-ai.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
      - name: orchestrator
        securityContext:
          {{- toYaml .Values.securityContext | nindent 12 }}
        image: "{{ .Values.orchestrator.image.repository }}:{{ .Values.orchestrator.image.tag }}"
        imagePullPolicy: {{ .Values.orchestrator.image.pullPolicy }}
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: {{ include "ticketsage-ai.fullname" . }}-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: {{ include "ticketsage-ai.fullname" . }}-secrets
              key: redis-url
        - name: KAFKA_BOOTSTRAP_SERVERS
          valueFrom:
            configMapKeyRef:
              name: {{ include "ticketsage-ai.fullname" . }}-config
              key: kafka-bootstrap-servers
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          {{- toYaml .Values.orchestrator.resources | nindent 12 }}
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: {{ include "ticketsage-ai.fullname" . }}-config
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
```

### **Values Configuration**
```yaml
# helm/ticketsage-ai/values-production.yaml
global:
  environment: production
  imageRegistry: 123456789012.dkr.ecr.us-west-2.amazonaws.com
  
orchestrator:
  replicaCount: 3
  image:
    repository: ticketsage/orchestrator
    tag: "1.0.0"
    pullPolicy: IfNotPresent
  resources:
    limits:
      cpu: 2000m
      memory: 4Gi
    requests:
      cpu: 1000m
      memory: 2Gi
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 20
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

agents:
  triage:
    replicaCount: 2
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi
  
  resolution:
    replicaCount: 3
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi

database:
  host: production-ticketsage-aurora.cluster-xyz.us-west-2.rds.amazonaws.com
  port: 5432
  name: ticketsage
  
redis:
  host: production-ticketsage-redis.abc123.cache.amazonaws.com
  port: 6379

kafka:
  bootstrapServers: "b-1.production-ticketsage-kafka.xyz123.c2.kafka.us-west-2.amazonaws.com:9092,b-2.production-ticketsage-kafka.xyz123.c2.kafka.us-west-2.amazonaws.com:9092,b-3.production-ticketsage-kafka.xyz123.c2.kafka.us-west-2.amazonaws.com:9092"

monitoring:
  prometheus:
    enabled: true
  grafana:
    enabled: true
  jaeger:
    enabled: true

ingress:
  enabled: true
  className: "alb"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:123456789012:certificate/abc123
  hosts:
    - host: api.ticketsage.ai
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: ticketsage-tls
      hosts:
        - api.ticketsage.ai
```

## GitOps Deployment Pipeline

### **ArgoCD Application Configuration**
```yaml
# argocd/applications/ticketsage-production.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ticketsage-production
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/ticketsage/ticketsage-ai
    targetRevision: main
    path: helm/ticketsage-ai
    helm:
      valueFiles:
        - values-production.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: ticketsage-production
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
```

### **CI/CD Pipeline (GitHub Actions)**
```yaml
# .github/workflows/deploy.yml
name: Deploy TicketSage AI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  AWS_REGION: us-west-2
  ECR_REPOSITORY: ticketsage

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    - name: Run tests
      run: pytest --cov=src tests/

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v3
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1
    
    - name: Build and push Docker images
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
      run: |
        # Build orchestrator
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY/orchestrator:$IMAGE_TAG -f docker/orchestrator/Dockerfile .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY/orchestrator:$IMAGE_TAG
        
        # Build agents
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY/triage-agent:$IMAGE_TAG -f docker/agents/triage/Dockerfile .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY/triage-agent:$IMAGE_TAG

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v3
    - name: Update Helm values
      run: |
        sed -i 's/tag: ".*"/tag: "'${{ github.sha }}'"/g' helm/ticketsage-ai/values-production.yaml
    - name: Commit and push changes
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add helm/ticketsage-ai/values-production.yaml
        git commit -m "Update image tag to ${{ github.sha }}"
        git push
```

This infrastructure automation provides a complete, production-ready deployment pipeline for TicketSage AI with infrastructure as code, automated testing, and GitOps-based deployment.

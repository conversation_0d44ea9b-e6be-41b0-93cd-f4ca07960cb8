"""
TicketSage AI Mock Data Generator for Industry Verticals
Generates realistic ticket data and cost savings demonstrations
"""

import random
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
from dataclasses import dataclass
from enum import Enum
import json

class IndustryVertical(Enum):
    MSP = "msp"
    HEALTHCARE = "healthcare"
    MANUFACTURING = "manufacturing"
    FINANCIAL = "financial"
    EDUCATION = "education"

@dataclass
class TicketData:
    id: str
    category: str
    subcategory: str
    priority: str
    description: str
    created_date: datetime
    resolved_date: datetime
    resolution_time_hours: float
    technician_id: str
    client_id: str
    cost: float
    automated: bool = False
    sla_met: bool = True

@dataclass
class CostMetrics:
    total_tickets: int
    total_cost: float
    average_resolution_time: float
    sla_compliance_rate: float
    automation_rate: float
    cost_per_ticket: float
    monthly_savings: float
    annual_roi: float

class IndustryMockDataGenerator:
    def __init__(self, vertical: IndustryVertical, organization_size: str = "medium"):
        self.vertical = vertical
        self.organization_size = organization_size
        self.baseline_metrics = self._load_baseline_metrics()
        self.ticket_categories = self._load_ticket_categories()
        
    def _load_baseline_metrics(self) -> Dict:
        """Load baseline metrics for each industry vertical"""
        metrics = {
            IndustryVertical.MSP: {
                "monthly_tickets": {"small": 5000, "medium": 15000, "large": 45000},
                "avg_resolution_hours": 4.2,
                "technician_hourly_rate": 85,
                "sla_compliance": 0.78,
                "escalation_rate": 0.25,
                "documentation_time": 0.75  # hours
            },
            IndustryVertical.HEALTHCARE: {
                "monthly_tickets": {"small": 2500, "medium": 8500, "large": 25000},
                "avg_resolution_hours": 2.8,
                "staff_hourly_rate": 45,
                "sla_compliance": 0.72,
                "compliance_overhead": 1.2,  # hours per ticket
                "patient_satisfaction": 3.2  # out of 5
            },
            IndustryVertical.MANUFACTURING: {
                "monthly_tickets": {"small": 800, "medium": 2200, "large": 6500},
                "avg_resolution_hours": 3.2,
                "downtime_cost_per_hour": 50000,
                "technician_hourly_rate": 95,
                "unplanned_downtime_rate": 0.35,
                "parts_delay_days": 2.5
            },
            IndustryVertical.FINANCIAL: {
                "monthly_tickets": {"small": 1200, "medium": 3800, "large": 12000},
                "avg_resolution_hours": 3.5,
                "compliance_officer_rate": 125,
                "regulatory_reporting_hours": 180,
                "risk_assessment_days": 2.8,
                "audit_hours_monthly": 45
            },
            IndustryVertical.EDUCATION: {
                "monthly_tickets": {"small": 2000, "medium": 6200, "large": 18500},
                "avg_resolution_hours": 2.1,
                "admin_hourly_rate": 38,
                "student_retention_rate": 0.88,
                "transcript_processing_hours": 3.5,
                "financial_aid_hours": 4.2
            }
        }
        return metrics[self.vertical]
    
    def _load_ticket_categories(self) -> Dict:
        """Load ticket categories specific to each vertical"""
        categories = {
            IndustryVertical.MSP: {
                "Password Reset": {"frequency": 0.25, "avg_time": 0.5, "automation_potential": 0.95},
                "Software Installation": {"frequency": 0.15, "avg_time": 2.0, "automation_potential": 0.75},
                "Network Connectivity": {"frequency": 0.12, "avg_time": 3.5, "automation_potential": 0.45},
                "Email Issues": {"frequency": 0.18, "avg_time": 1.5, "automation_potential": 0.80},
                "Hardware Failure": {"frequency": 0.08, "avg_time": 8.0, "automation_potential": 0.25},
                "Security Incident": {"frequency": 0.05, "avg_time": 12.0, "automation_potential": 0.15},
                "Backup Issues": {"frequency": 0.10, "avg_time": 4.0, "automation_potential": 0.60},
                "Performance Issues": {"frequency": 0.07, "avg_time": 6.0, "automation_potential": 0.35}
            },
            IndustryVertical.HEALTHCARE: {
                "Patient Registration": {"frequency": 0.30, "avg_time": 1.5, "automation_potential": 0.85},
                "Insurance Verification": {"frequency": 0.20, "avg_time": 2.5, "automation_potential": 0.70},
                "Appointment Scheduling": {"frequency": 0.15, "avg_time": 1.0, "automation_potential": 0.90},
                "Medical Records": {"frequency": 0.12, "avg_time": 3.0, "automation_potential": 0.60},
                "Billing Inquiries": {"frequency": 0.10, "avg_time": 2.0, "automation_potential": 0.75},
                "Compliance Documentation": {"frequency": 0.08, "avg_time": 4.0, "automation_potential": 0.80},
                "Prescription Management": {"frequency": 0.05, "avg_time": 1.5, "automation_potential": 0.65}
            },
            IndustryVertical.MANUFACTURING: {
                "Equipment Maintenance": {"frequency": 0.35, "avg_time": 4.0, "automation_potential": 0.70},
                "Quality Control": {"frequency": 0.20, "avg_time": 2.5, "automation_potential": 0.60},
                "Inventory Management": {"frequency": 0.15, "avg_time": 1.5, "automation_potential": 0.85},
                "Safety Incidents": {"frequency": 0.08, "avg_time": 6.0, "automation_potential": 0.30},
                "Production Planning": {"frequency": 0.12, "avg_time": 3.0, "automation_potential": 0.55},
                "Equipment Failure": {"frequency": 0.10, "avg_time": 8.0, "automation_potential": 0.40}
            },
            IndustryVertical.FINANCIAL: {
                "Account Opening": {"frequency": 0.25, "avg_time": 3.0, "automation_potential": 0.80},
                "Compliance Review": {"frequency": 0.20, "avg_time": 4.5, "automation_potential": 0.70},
                "Fraud Investigation": {"frequency": 0.15, "avg_time": 6.0, "automation_potential": 0.45},
                "Loan Processing": {"frequency": 0.12, "avg_time": 5.0, "automation_potential": 0.65},
                "Risk Assessment": {"frequency": 0.10, "avg_time": 4.0, "automation_potential": 0.75},
                "Regulatory Reporting": {"frequency": 0.08, "avg_time": 8.0, "automation_potential": 0.85},
                "Customer Service": {"frequency": 0.10, "avg_time": 2.0, "automation_potential": 0.60}
            },
            IndustryVertical.EDUCATION: {
                "Student Enrollment": {"frequency": 0.25, "avg_time": 2.0, "automation_potential": 0.85},
                "Grade Processing": {"frequency": 0.20, "avg_time": 1.5, "automation_potential": 0.90},
                "Financial Aid": {"frequency": 0.15, "avg_time": 4.0, "automation_potential": 0.70},
                "Transcript Requests": {"frequency": 0.12, "avg_time": 3.5, "automation_potential": 0.95},
                "Course Registration": {"frequency": 0.10, "avg_time": 1.0, "automation_potential": 0.88},
                "Student Support": {"frequency": 0.08, "avg_time": 2.5, "automation_potential": 0.50},
                "Faculty Management": {"frequency": 0.10, "avg_time": 3.0, "automation_potential": 0.60}
            }
        }
        return categories[self.vertical]
    
    def generate_historical_tickets(self, months: int = 12) -> List[TicketData]:
        """Generate realistic historical ticket data"""
        tickets = []
        monthly_volume = self.baseline_metrics["monthly_tickets"][self.organization_size]
        
        for month in range(months):
            # Add seasonal variation
            seasonal_factor = 1 + 0.2 * np.sin(2 * np.pi * month / 12)
            month_tickets = int(monthly_volume * seasonal_factor)
            
            for i in range(month_tickets):
                ticket = self._generate_single_ticket(month, i)
                tickets.append(ticket)
        
        return tickets
    
    def _generate_single_ticket(self, month: int, ticket_num: int) -> TicketData:
        """Generate a single realistic ticket"""
        # Select category based on frequency distribution
        category = self._select_weighted_category()
        category_data = self.ticket_categories[category]
        
        # Generate timing
        created_date = datetime.now() - timedelta(days=30*month) + timedelta(
            days=random.randint(0, 29),
            hours=random.randint(8, 18),
            minutes=random.randint(0, 59)
        )
        
        # Generate resolution time with realistic variation
        base_time = category_data["avg_time"]
        resolution_time = max(0.1, np.random.lognormal(np.log(base_time), 0.5))
        resolved_date = created_date + timedelta(hours=resolution_time)
        
        # Calculate cost based on vertical
        cost = self._calculate_ticket_cost(category, resolution_time)
        
        # Determine SLA compliance
        sla_target = self._get_sla_target(category)
        sla_met = resolution_time <= sla_target
        
        return TicketData(
            id=f"TKT-{month:02d}-{ticket_num:04d}",
            category=category,
            subcategory=self._generate_subcategory(category),
            priority=self._generate_priority(),
            description=self._generate_description(category),
            created_date=created_date,
            resolved_date=resolved_date,
            resolution_time_hours=resolution_time,
            technician_id=f"TECH-{random.randint(1, 50):03d}",
            client_id=f"CLIENT-{random.randint(1, 100):03d}",
            cost=cost,
            sla_met=sla_met
        )
    
    def _select_weighted_category(self) -> str:
        """Select category based on frequency weights"""
        categories = list(self.ticket_categories.keys())
        weights = [self.ticket_categories[cat]["frequency"] for cat in categories]
        return np.random.choice(categories, p=weights)
    
    def _calculate_ticket_cost(self, category: str, resolution_time: float) -> float:
        """Calculate cost based on vertical-specific rates"""
        if self.vertical == IndustryVertical.MSP:
            return resolution_time * self.baseline_metrics["technician_hourly_rate"]
        elif self.vertical == IndustryVertical.HEALTHCARE:
            return resolution_time * self.baseline_metrics["staff_hourly_rate"]
        elif self.vertical == IndustryVertical.MANUFACTURING:
            # Include downtime costs for critical categories
            base_cost = resolution_time * self.baseline_metrics["technician_hourly_rate"]
            if category in ["Equipment Failure", "Equipment Maintenance"]:
                downtime_cost = resolution_time * self.baseline_metrics["downtime_cost_per_hour"]
                return base_cost + downtime_cost
            return base_cost
        elif self.vertical == IndustryVertical.FINANCIAL:
            return resolution_time * self.baseline_metrics["compliance_officer_rate"]
        elif self.vertical == IndustryVertical.EDUCATION:
            return resolution_time * self.baseline_metrics["admin_hourly_rate"]
    
    def _get_sla_target(self, category: str) -> float:
        """Get SLA target hours for category"""
        sla_targets = {
            "Password Reset": 1.0,
            "Patient Registration": 2.0,
            "Equipment Failure": 4.0,
            "Fraud Investigation": 8.0,
            "Student Enrollment": 24.0
        }
        return sla_targets.get(category, 4.0)  # Default 4 hours
    
    def _generate_subcategory(self, category: str) -> str:
        """Generate realistic subcategory"""
        subcategories = {
            "Password Reset": ["Windows Login", "Email Password", "Application Access"],
            "Patient Registration": ["New Patient", "Update Info", "Insurance Change"],
            "Equipment Maintenance": ["Preventive", "Corrective", "Emergency"]
        }
        return random.choice(subcategories.get(category, ["General"]))
    
    def _generate_priority(self) -> str:
        """Generate priority with realistic distribution"""
        priorities = ["Low", "Medium", "High", "Critical"]
        weights = [0.4, 0.35, 0.20, 0.05]
        return np.random.choice(priorities, p=weights)
    
    def _generate_description(self, category: str) -> str:
        """Generate realistic ticket description"""
        descriptions = {
            "Password Reset": "User unable to access system after password expiration",
            "Patient Registration": "New patient needs to be registered in the system",
            "Equipment Maintenance": "Scheduled maintenance required for production equipment"
        }
        return descriptions.get(category, f"Issue related to {category}")
    
    def calculate_current_state_metrics(self, tickets: List[TicketData]) -> CostMetrics:
        """Calculate current state cost metrics"""
        total_tickets = len(tickets)
        total_cost = sum(ticket.cost for ticket in tickets)
        avg_resolution_time = np.mean([ticket.resolution_time_hours for ticket in tickets])
        sla_compliance = sum(1 for ticket in tickets if ticket.sla_met) / total_tickets
        
        return CostMetrics(
            total_tickets=total_tickets,
            total_cost=total_cost,
            average_resolution_time=avg_resolution_time,
            sla_compliance_rate=sla_compliance,
            automation_rate=0.0,  # Current state has no automation
            cost_per_ticket=total_cost / total_tickets,
            monthly_savings=0.0,
            annual_roi=0.0
        )
    
    def project_future_state_metrics(self, current_metrics: CostMetrics) -> CostMetrics:
        """Project future state with TicketSage AI"""
        # Calculate automation potential by category
        automation_rates = {
            IndustryVertical.MSP: 0.32,
            IndustryVertical.HEALTHCARE: 0.41,
            IndustryVertical.MANUFACTURING: 0.29,
            IndustryVertical.FINANCIAL: 0.35,
            IndustryVertical.EDUCATION: 0.38
        }
        
        efficiency_gains = {
            IndustryVertical.MSP: 0.57,  # 57% reduction in resolution time
            IndustryVertical.HEALTHCARE: 0.61,
            IndustryVertical.MANUFACTURING: 0.44,
            IndustryVertical.FINANCIAL: 0.60,
            IndustryVertical.EDUCATION: 0.57
        }
        
        automation_rate = automation_rates[self.vertical]
        efficiency_gain = efficiency_gains[self.vertical]
        
        # Calculate savings
        automated_tickets = int(current_metrics.total_tickets * automation_rate)
        automated_savings = automated_tickets * current_metrics.cost_per_ticket
        
        remaining_tickets = current_metrics.total_tickets - automated_tickets
        efficiency_savings = remaining_tickets * current_metrics.cost_per_ticket * efficiency_gain
        
        total_monthly_savings = automated_savings + efficiency_savings
        annual_savings = total_monthly_savings * 12
        
        # Assume implementation cost of $100K
        implementation_cost = 100000
        annual_roi = (annual_savings - implementation_cost) / implementation_cost * 100
        
        return CostMetrics(
            total_tickets=current_metrics.total_tickets,
            total_cost=current_metrics.total_cost - total_monthly_savings,
            average_resolution_time=current_metrics.average_resolution_time * (1 - efficiency_gain),
            sla_compliance_rate=min(0.98, current_metrics.sla_compliance_rate + 0.16),
            automation_rate=automation_rate,
            cost_per_ticket=current_metrics.cost_per_ticket * (1 - efficiency_gain),
            monthly_savings=total_monthly_savings,
            annual_roi=annual_roi
        )

# Example usage
if __name__ == "__main__":
    # Generate mock data for MSP
    generator = IndustryMockDataGenerator(IndustryVertical.MSP, "medium")
    tickets = generator.generate_historical_tickets(12)
    
    current_metrics = generator.calculate_current_state_metrics(tickets)
    future_metrics = generator.project_future_state_metrics(current_metrics)
    
    print(f"Current State - Monthly Cost: ${current_metrics.total_cost:,.2f}")
    print(f"Future State - Monthly Savings: ${future_metrics.monthly_savings:,.2f}")
    print(f"Annual ROI: {future_metrics.annual_roi:.1f}%")

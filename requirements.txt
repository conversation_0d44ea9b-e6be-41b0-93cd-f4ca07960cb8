# TicketSage AI Demo Environment Requirements

# Core web framework
streamlit>=1.28.0
fastapi>=0.100.0
uvicorn>=0.23.0

# Data manipulation and analysis
pandas>=2.0.0
numpy>=1.24.0

# Visualization
plotly>=5.15.0

# Date/time handling
python-dateutil>=2.8.0

# Excel file support
openpyxl>=3.1.0

# Template engine
jinja2>=3.1.0

# Markdown processing
markdown>=3.4.0

# Data validation
pydantic>=2.0.0

# Machine learning (optional for advanced features)
scikit-learn>=1.3.0
torch>=2.0.0

# Database connectivity (for future expansion)
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0

# Redis connectivity (for future expansion)
redis>=4.5.0

# HTTP requests
requests>=2.31.0

# Configuration management
python-dotenv>=1.0.0

# Logging
structlog>=23.1.0

# Testing (development)
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Code quality (development)
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

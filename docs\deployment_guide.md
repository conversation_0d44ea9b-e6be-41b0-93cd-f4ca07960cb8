# TicketSage AI Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying TicketSage AI in various environments, from local development to production Kubernetes clusters.

## Prerequisites

### System Requirements

**Minimum Requirements (Development)**:
- 4 CPU cores
- 16 GB RAM
- 100 GB storage
- Docker and Docker Compose
- Python 3.8+

**Recommended Requirements (Production)**:
- 16+ CPU cores
- 64+ GB RAM
- 1 TB SSD storage
- Kubernetes cluster
- Load balancer
- Monitoring stack

### Software Dependencies

```bash
# Required software
- Docker 20.10+
- Kubernetes 1.24+
- Helm 3.8+
- kubectl
- Python 3.8+
- Node.js 16+ (for frontend)
```

## Local Development Setup

### 1. Clone Repository and Setup Environment

```bash
# Clone repository
git clone https://github.com/ticketsage/ticketsage-ai.git
cd ticketsage-ai

# Setup Python environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# Setup demo environment
python setup_demo_environment.py
```

### 2. Start Infrastructure Services

```bash
# Start required services with Docker Compose
docker-compose -f docker-compose.dev.yml up -d

# Verify services are running
docker-compose ps
```

**docker-compose.dev.yml**:
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ticketsage
      POSTGRES_USER: ticketsage
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9092:9092"
    depends_on:
      - zookeeper

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage

volumes:
  postgres_data:
  redis_data:
  qdrant_data:
```

### 3. Initialize Database and Services

```bash
# Run database migrations
alembic upgrade head

# Initialize vector database
python scripts/init_qdrant.py

# Load sample data
python scripts/load_sample_data.py

# Start the application
uvicorn src.api.main:app --reload --host 0.0.0.0 --port 8000
```

### 4. Start Demo Interface

```bash
# Launch ROI calculator
python launch_demo.py

# Access at http://localhost:8501
```

## Production Deployment

### 1. Kubernetes Cluster Setup

#### Prerequisites
```bash
# Install required tools
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
curl https://get.helm.sh/helm-v3.12.0-linux-amd64.tar.gz | tar xz

# Verify cluster access
kubectl cluster-info
```

#### Create Namespace
```bash
kubectl create namespace ticketsage-ai
kubectl label namespace ticketsage-ai istio-injection=enabled
```

### 2. Install Dependencies

#### Istio Service Mesh
```bash
# Install Istio
curl -L https://istio.io/downloadIstio | sh -
cd istio-*
export PATH=$PWD/bin:$PATH
istioctl install --set values.defaultRevision=default

# Verify installation
kubectl get pods -n istio-system
```

#### Kafka (Strimzi Operator)
```bash
# Install Strimzi operator
kubectl create -f 'https://strimzi.io/install/latest?namespace=ticketsage-ai'

# Deploy Kafka cluster
kubectl apply -f infrastructure/kubernetes/kafka-cluster.yaml
```

#### Monitoring Stack
```bash
# Add Prometheus Helm repository
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

# Install Prometheus and Grafana
helm install monitoring prometheus-community/kube-prometheus-stack \
  --namespace ticketsage-ai \
  --set grafana.adminPassword=admin123
```

### 3. Deploy Application Components

#### Configuration Management
```bash
# Create ConfigMap
kubectl create configmap ticketsage-config \
  --from-file=config/production.yaml \
  --namespace=ticketsage-ai

# Create Secrets
kubectl create secret generic ticketsage-secrets \
  --from-literal=database-password=your_secure_password \
  --from-literal=jwt-secret=your_jwt_secret \
  --from-literal=api-key=your_api_key \
  --namespace=ticketsage-ai
```

#### Database Setup
```bash
# Deploy PostgreSQL
helm install postgresql bitnami/postgresql \
  --namespace ticketsage-ai \
  --set auth.postgresPassword=your_secure_password \
  --set auth.database=ticketsage

# Deploy Redis
helm install redis bitnami/redis \
  --namespace ticketsage-ai \
  --set auth.password=your_redis_password
```

#### Application Deployment
```bash
# Deploy using Helm chart
helm install ticketsage-ai ./helm/ticketsage-ai \
  --namespace ticketsage-ai \
  --values helm/ticketsage-ai/values.production.yaml
```

**helm/ticketsage-ai/values.production.yaml**:
```yaml
replicaCount: 3

image:
  repository: ticketsage/api
  tag: "1.0.0"
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: "istio"
  annotations:
    kubernetes.io/ingress.class: istio
  hosts:
    - host: api.ticketsage.ai
      paths:
        - path: /
          pathType: Prefix

resources:
  limits:
    cpu: 2000m
    memory: 4Gi
  requests:
    cpu: 1000m
    memory: 2Gi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 20
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

postgresql:
  enabled: false
  external:
    host: postgresql
    port: 5432
    database: ticketsage

redis:
  enabled: false
  external:
    host: redis-master
    port: 6379

kafka:
  enabled: false
  external:
    brokers: "kafka-kafka-bootstrap:9092"

monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
```

### 4. Verify Deployment

```bash
# Check pod status
kubectl get pods -n ticketsage-ai

# Check services
kubectl get services -n ticketsage-ai

# Check ingress
kubectl get ingress -n ticketsage-ai

# View logs
kubectl logs -f deployment/ticketsage-api -n ticketsage-ai
```

### 5. Configure Monitoring

#### Grafana Dashboards
```bash
# Import TicketSage dashboards
kubectl apply -f monitoring/grafana-dashboards.yaml
```

#### Alerting Rules
```bash
# Apply Prometheus alerting rules
kubectl apply -f monitoring/prometheus-rules.yaml
```

**monitoring/prometheus-rules.yaml**:
```yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: ticketsage-alerts
  namespace: ticketsage-ai
spec:
  groups:
  - name: ticketsage.rules
    rules:
    - alert: HighTicketProcessingTime
      expr: histogram_quantile(0.95, ticket_processing_duration_seconds) > 300
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High ticket processing time detected"
        description: "95th percentile processing time is {{ $value }} seconds"

    - alert: SLAComplianceBelow80
      expr: sla_compliance_rate < 0.8
      for: 10m
      labels:
        severity: critical
      annotations:
        summary: "SLA compliance below 80%"
        description: "Current SLA compliance is {{ $value }}%"

    - alert: AgentUnresponsive
      expr: up{job="ticketsage-agents"} == 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Agent is unresponsive"
        description: "Agent {{ $labels.instance }} has been down for more than 1 minute"
```

## Environment-Specific Configurations

### Development Environment

**config/development.yaml**:
```yaml
debug: true
log_level: DEBUG

database:
  host: localhost
  port: 5432
  name: ticketsage_dev
  user: ticketsage
  password: dev_password

redis:
  host: localhost
  port: 6379
  db: 0

kafka:
  bootstrap_servers: ["localhost:9092"]
  
qdrant:
  host: localhost
  port: 6333

agents:
  max_concurrent_tasks: 5
  heartbeat_interval: 30

security:
  jwt_secret: dev_jwt_secret
  token_expiry: 3600
```

### Staging Environment

**config/staging.yaml**:
```yaml
debug: false
log_level: INFO

database:
  host: postgres-staging
  port: 5432
  name: ticketsage_staging
  user: ticketsage
  password: ${DATABASE_PASSWORD}

redis:
  host: redis-staging
  port: 6379
  db: 0

kafka:
  bootstrap_servers: ["kafka-staging:9092"]

qdrant:
  host: qdrant-staging
  port: 6333

agents:
  max_concurrent_tasks: 10
  heartbeat_interval: 15

security:
  jwt_secret: ${JWT_SECRET}
  token_expiry: 1800
```

### Production Environment

**config/production.yaml**:
```yaml
debug: false
log_level: WARNING

database:
  host: postgres-prod
  port: 5432
  name: ticketsage_prod
  user: ticketsage
  password: ${DATABASE_PASSWORD}
  pool_size: 20
  max_overflow: 30

redis:
  host: redis-prod
  port: 6379
  db: 0
  password: ${REDIS_PASSWORD}

kafka:
  bootstrap_servers: ["kafka-prod-1:9092", "kafka-prod-2:9092", "kafka-prod-3:9092"]
  security_protocol: SASL_SSL
  sasl_mechanism: PLAIN
  sasl_username: ${KAFKA_USERNAME}
  sasl_password: ${KAFKA_PASSWORD}

qdrant:
  host: qdrant-prod
  port: 6333
  api_key: ${QDRANT_API_KEY}

agents:
  max_concurrent_tasks: 50
  heartbeat_interval: 10

security:
  jwt_secret: ${JWT_SECRET}
  token_expiry: 900
  
monitoring:
  prometheus_endpoint: /metrics
  health_check_endpoint: /health
  
rate_limiting:
  requests_per_minute: 1000
  burst_size: 100
```

## Backup and Recovery

### Database Backup

```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="ticketsage_backup_${DATE}.sql"

# Create backup
kubectl exec -n ticketsage-ai postgresql-0 -- pg_dump -U ticketsage ticketsage > ${BACKUP_DIR}/${BACKUP_FILE}

# Compress backup
gzip ${BACKUP_DIR}/${BACKUP_FILE}

# Upload to cloud storage (example with AWS S3)
aws s3 cp ${BACKUP_DIR}/${BACKUP_FILE}.gz s3://ticketsage-backups/postgresql/
```

### Vector Database Backup

```bash
# Backup Qdrant collections
curl -X POST "http://qdrant:6333/collections/knowledge_base/snapshots"
curl -X POST "http://qdrant:6333/collections/ticket_embeddings/snapshots"
```

### Disaster Recovery

```bash
# Database restore
kubectl exec -n ticketsage-ai postgresql-0 -- psql -U ticketsage -d ticketsage < backup.sql

# Application restart
kubectl rollout restart deployment/ticketsage-api -n ticketsage-ai

# Verify system health
kubectl get pods -n ticketsage-ai
curl -f http://api.ticketsage.ai/health
```

## Troubleshooting

### Common Issues

**Pods not starting**:
```bash
# Check pod events
kubectl describe pod <pod-name> -n ticketsage-ai

# Check logs
kubectl logs <pod-name> -n ticketsage-ai

# Check resource constraints
kubectl top pods -n ticketsage-ai
```

**Database connection issues**:
```bash
# Test database connectivity
kubectl exec -it postgresql-0 -n ticketsage-ai -- psql -U ticketsage -d ticketsage -c "SELECT 1;"

# Check service endpoints
kubectl get endpoints -n ticketsage-ai
```

**Performance issues**:
```bash
# Check resource usage
kubectl top nodes
kubectl top pods -n ticketsage-ai

# Review metrics in Grafana
# Access Grafana at http://grafana.ticketsage.ai
```

### Health Checks

```bash
# Application health
curl http://api.ticketsage.ai/health

# Database health
kubectl exec -n ticketsage-ai postgresql-0 -- pg_isready

# Redis health
kubectl exec -n ticketsage-ai redis-master-0 -- redis-cli ping

# Kafka health
kubectl exec -n ticketsage-ai kafka-0 -- kafka-topics.sh --bootstrap-server localhost:9092 --list
```

This deployment guide provides comprehensive instructions for setting up TicketSage AI in various environments, from development to production.
